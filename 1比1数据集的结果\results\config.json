{"project": {"name": "pulsar-coatnet", "version": "3.0.0", "description": "Standardized CoAtNet-based pulsar classification system"}, "data": {"modality": "MULTIMODAL", "data_root": "D:/pulsarSuanfa/datasets/HTRU", "normalize": true, "num_workers": 4, "pin_memory": true}, "model": {"name": "coatnet", "coatnet": {"name": "coatnet", "num_blocks": [2, 2, 6, 8, 2], "channels": [96, 128, 256, 512, 1024], "block_types": ["C", "C", "T", "T"], "image_size": [64, 64], "in_channels": 3, "num_classes": 2, "dropout": 0.2}}, "augmentation": {"enabled": true, "phase_shift": {"enabled": true, "probability": 0.7, "max_shift": 1.0}, "frequency_shift": {"enabled": true, "probability": 0.3, "max_shift": 0.1}, "time_shift": {"enabled": true, "probability": 0.3, "max_shift": 0.1}, "brightness_scaling": {"enabled": true, "probability": 0.5, "scale_range": [0.8, 1.2]}, "noise": {"enabled": true, "probability": 0.4, "noise_level": 0.01}}, "training": {"batch_size": 32, "epochs": 100, "learning_rate": 0.001, "weight_decay": 0.01, "optimizer": "adamw", "scheduler": {"type": "cosine", "warmup_epochs": 10, "min_lr": 1e-05, "total_epochs": 100}, "early_stopping": {"patience": 15, "min_delta": 0.001}, "loss": {"type": "cross_entropy", "smoothing": 0.1}}, "device": {"use_cuda": true, "device_id": 0, "compile_model": false, "benchmark": true, "deterministic": false}, "output": {"base_dir": "outputs", "save_best_model": true, "save_last_model": true, "save_optimizer": true, "log_interval": 10, "eval_interval": 1}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "save_to_file": true, "console_output": true}, "evaluation": {"metrics": ["accuracy", "precision", "recall", "f1_score", "auc_roc", "confusion_matrix"], "save_predictions": true, "save_probabilities": true, "generate_plots": true, "misclassification_analysis": true}, "performance_targets": {"accuracy": 99.0, "precision": 99.0, "recall": 99.0, "f1_score": 99.0}}