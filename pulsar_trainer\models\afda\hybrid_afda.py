"""
混合AFDA模块 (Hybrid AFDA)
MS-AFDA + PC-AFDA混合架构
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Dict, Any
from .afda_base import AFDAModule
from .ms_afda import MultiScaleAFDA
from .pc_afda import PhysicsConstrainedAFDA


class HybridAFDA(AFDAModule):
    """MS-AFDA + PC-AFDA混合架构"""
    
    def __init__(self, 
                 scales: List[int] = [16, 32, 64],
                 dispersion_weight: float = 0.4,
                 periodicity_weight: float = 0.6,
                 fusion_strategy: str = "adaptive_weighted",
                 physical_constraints: Dict[str, Any] = None,
                 adaptive_fusion: Dict[str, Any] = None,
                 **kwargs):
        super().__init__(**kwargs)
        
        # 多尺度组件参数
        self.scales = scales
        self.fusion_strategy = fusion_strategy
        self.num_scales = len(scales)
        
        # 物理约束组件参数
        self.dispersion_weight = dispersion_weight
        self.periodicity_weight = periodicity_weight
        
        # 自适应融合参数
        if adaptive_fusion is None:
            adaptive_fusion = {
                "threshold": 0.5,
                "temperature": 1.0,
                "dropout": 0.1
            }
        self.adaptive_fusion_config = adaptive_fusion
        
        # 初始化多尺度组件
        self.scale_attention_heads = nn.ModuleList([
            nn.Sequential(
                nn.Linear(self.feature_dim, self.attention_heads),
                nn.GELU(),
                nn.Linear(self.attention_heads, self.attention_heads)
            ) for _ in scales
        ])
        
        # 多尺度特征融合权重
        self.fusion_weights = nn.Parameter(torch.ones(self.num_scales))
        self.fusion_mlp = nn.Sequential(
            nn.Linear(self.feature_dim * self.num_scales, self.feature_dim),
            nn.GELU(),
            nn.Linear(self.feature_dim, self.num_scales),
            nn.Softmax(dim=-1)
        )
        
        # 初始化物理约束组件
        if physical_constraints is None:
            physical_constraints = {
                "dispersion": {"enabled": True, "weight": dispersion_weight},
                "periodicity": {"enabled": True, "weight": periodicity_weight}
            }
        self.physical_constraints = physical_constraints
        
        # 色散关系参数
        self.dispersion_enabled = physical_constraints.get("dispersion", {}).get("enabled", True)
        if self.dispersion_enabled:
            self.dispersion_coeff = nn.Parameter(torch.tensor(1.0))
            self.dispersion_mlp = nn.Sequential(
                nn.Linear(self.feature_dim, self.feature_dim // 2),
                nn.GELU(),
                nn.Linear(self.feature_dim // 2, self.frequency_bins),
                nn.Sigmoid()
            )
        
        # 周期性约束参数
        self.periodicity_enabled = physical_constraints.get("periodicity", {}).get("enabled", True)
        if self.periodicity_enabled:
            self.period_detector = nn.Sequential(
                nn.Linear(self.feature_dim, self.feature_dim // 2),
                nn.GELU(),
                nn.Linear(self.feature_dim // 2, self.frequency_bins),
                nn.Softmax(dim=-1)
            )
            self.periodicity_enhancer = nn.Parameter(torch.ones(self.frequency_bins))
        
        # 物理约束融合网络
        self.constraint_fusion = nn.Sequential(
            nn.Linear(self.frequency_bins * 2, self.frequency_bins),
            nn.GELU(),
            nn.Linear(self.frequency_bins, self.frequency_bins),
            nn.Sigmoid()
        )
        
        # 混合架构融合网络
        self.hybrid_fusion = nn.Sequential(
            nn.Linear(self.feature_dim * 2, self.feature_dim),
            nn.GELU(),
            nn.Dropout(adaptive_fusion["dropout"]),
            nn.Linear(self.feature_dim, self.feature_dim)
        )
        
        # 自适应权重控制
        self.adaptive_gate = nn.Sequential(
            nn.Linear(self.feature_dim, 2),
            nn.Softmax(dim=-1)
        )
        
    def _multi_scale_fft(self, x: torch.Tensor) -> List[torch.Tensor]:
        """多尺度FFT变换"""
        B, C, H, W = x.shape
        x_flat = x.view(B, C, H * W).permute(0, 2, 1)  # (B, H*W, C)
        
        multi_scale_features = []
        
        for i, scale in enumerate(self.scales):
            if H * W > scale:
                indices = torch.linspace(0, H*W-1, scale, dtype=torch.long, device=x.device)
                x_scale = x_flat[:, indices, :]
            else:
                pad_size = scale - H * W
                x_scale = F.pad(x_flat, (0, 0, 0, pad_size))
            
            x_heads = x_scale.view(B, scale, self.attention_heads, self.head_dim).permute(0, 2, 1, 3)
            F_fft = torch.fft.rfft(x_heads, dim=2, norm='ortho')
            multi_scale_features.append(F_fft)
            
        return multi_scale_features
        
    def _physics_constraints(self, freq_features: torch.Tensor) -> torch.Tensor:
        """应用物理约束"""
        B, attention_heads, freq_bins, head_dim = freq_features.shape
        
        # 创建频率轴
        frequencies = torch.linspace(0, 1, freq_bins, device=freq_features.device)
        frequencies = frequencies.unsqueeze(0).expand(B, -1)
        
        # 平均多头特征用于物理约束计算
        avg_features = freq_features.mean(dim=1)
        
        # 色散约束
        dispersion_weights = torch.ones(B, freq_bins, device=freq_features.device)
        if self.dispersion_enabled:
            freq_safe = frequencies + 1e-6
            dispersion_base = 1.0 / (freq_safe ** 2)
            dispersion_base = dispersion_base / dispersion_base.sum(dim=-1, keepdim=True)
            
            global_repr = avg_features.mean(dim=-1)
            adaptive_dispersion = self.dispersion_mlp(global_repr)
            dispersion_weights = dispersion_base * adaptive_dispersion * self.dispersion_coeff
        
        # 周期性约束
        periodicity_weights = torch.ones(B, freq_bins, device=freq_features.device)
        if self.periodicity_enabled:
            global_repr = avg_features.mean(dim=-1)
            period_weights = self.period_detector(global_repr)
            periodicity_weights = period_weights * self.periodicity_enhancer.unsqueeze(0)
        
        # 融合物理约束
        constraint_input = torch.cat([dispersion_weights, periodicity_weights], dim=-1)
        physics_weights = self.constraint_fusion(constraint_input)
        
        # 扩展到多头维度
        physics_weights = physics_weights.unsqueeze(1).unsqueeze(-1)
        physics_weights = physics_weights.expand(-1, attention_heads, -1, -1)
        
        return physics_weights
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """混合AFDA前向传播"""
        original_shape = x.shape
        B, C, H, W = x.shape
        
        # 1. 预归一化
        x_flat = x.view(B, C, H * W).permute(0, 2, 1)
        x_norm = self.pre_norm(x_flat)
        
        # 2. 多尺度FFT变换
        multi_scale_fft = self._multi_scale_fft(x)
        
        # 3. 对每个尺度应用物理约束注意力
        enhanced_features = []
        for i, F_fft in enumerate(multi_scale_fft):
            # 应用物理约束
            physics_weights = self._physics_constraints(F_fft)
            
            # 计算该尺度的自适应参数
            scale_repr = F_fft.mean(dim=(1, 2))
            adaptive_params = self.scale_attention_heads[i](scale_repr)
            
            freq_bins = F_fft.shape[2]
            adaptive_scale = adaptive_params.unsqueeze(-1).unsqueeze(-1)
            adaptive_scale = adaptive_scale.expand(-1, -1, freq_bins, 1)
            
            # 结合基础滤波器、自适应权重和物理约束
            base_filter = self.base_filter[:, :freq_bins, :]
            base_bias = self.base_bias[:, :freq_bins, :]
            
            effective_filter = base_filter * (1 + adaptive_scale) * physics_weights[:, :, :freq_bins, :]
            effective_bias = base_bias
            
            # 频域增强
            F_fft_mod = F_fft * effective_filter + effective_bias
            F_fft_nl = self.complex_activation(F_fft_mod)
            
            enhanced_features.append(F_fft_nl)
        
        # 4. 自适应特征融合
        max_freq_bins = max([feat.shape[2] for feat in enhanced_features])
        aligned_features = []
        
        for feat in enhanced_features:
            if feat.shape[2] < max_freq_bins:
                pad_size = max_freq_bins - feat.shape[2]
                feat_padded = F.pad(feat, (0, 0, 0, pad_size))
                aligned_features.append(feat_padded)
            else:
                aligned_features.append(feat)
        
        # 计算融合权重
        scale_representations = [feat.mean(dim=(1, 2)) for feat in aligned_features]
        concat_repr = torch.cat(scale_representations, dim=-1)
        fusion_weights = self.fusion_mlp(concat_repr)
        
        # 加权融合
        fused_feature = torch.zeros_like(aligned_features[0])
        for i, feat in enumerate(aligned_features):
            weight = fusion_weights[:, i:i+1, None, None]
            fused_feature += weight * feat
        
        # 5. IFFT逆变换
        x_filtered = torch.fft.irfft(fused_feature, dim=2, n=H*W, norm='ortho')
        x_filtered = x_filtered.permute(0, 2, 1, 3).reshape(B, H*W, C)
        
        # 6. 残差连接
        output = x_norm + self.dropout(x_filtered)
        output = output.permute(0, 2, 1).view(B, C, H, W)
        
        return output
