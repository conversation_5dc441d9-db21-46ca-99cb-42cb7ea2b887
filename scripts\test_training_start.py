#!/usr/bin/env python3
"""
测试3通道多模态训练启动
验证训练流程能够正常启动
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import torch.nn as nn
from pathlib import Path
import logging

from pulsar_trainer.data.dataset import create_dataloaders_with_config
from pulsar_trainer.models.coatnet import CoAtNet
from pulsar_trainer.utils.config import load_config

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_training_initialization():
    """测试训练初始化"""
    print("="*60)
    print("3通道多模态训练启动测试")
    print("="*60)

    try:
        # 1. 加载配置
        config_path = "pulsar_trainer/config/coatnet_config.yaml"
        print(f"加载配置文件: {config_path}")

        # 手动加载配置
        import yaml
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        print("✓ 配置加载成功")
        print(f"  数据模态: {config['data']['modality']}")
        print(f"  输入通道: {config['model']['coatnet']['in_channels']}")
        print(f"  数据根目录: {config['data']['data_root']}")

        # 2. 创建数据加载器
        print("\n创建数据加载器...")

        # 创建简化的配置对象
        class SimpleConfig:
            def __init__(self, config_dict):
                for key, value in config_dict.items():
                    if isinstance(value, dict):
                        setattr(self, key, SimpleConfig(value))
                    else:
                        setattr(self, key, value)

        simple_config = SimpleConfig(config)

        # 手动创建数据集
        from pulsar_trainer.data.dataset import MultiModalPulsarDataset
        from torch.utils.data import DataLoader

        train_dataset = MultiModalPulsarDataset(
            split='train',
            data_root=config['data']['data_root'],
            augmentation_config=None,  # 暂时禁用数据增强
            normalize=True
        )

        train_loader = DataLoader(
            train_dataset,
            batch_size=config['training']['batch_size'],
            shuffle=True,
            num_workers=0
        )

        print("✓ 数据加载器创建成功")
        print(f"  训练样本数: {len(train_dataset)}")
        print(f"  批次数: {len(train_loader)}")

        # 3. 创建模型
        print("\n创建模型...")
        model_config = config['model']['coatnet']

        model = CoAtNet(
            image_size=tuple(model_config['image_size']),
            in_channels=model_config['in_channels'],
            num_blocks=model_config['num_blocks'],
            channels=model_config['channels'],
            num_classes=model_config['num_classes'],
            block_types=model_config['block_types']
        )

        print("✓ 模型创建成功")
        total_params = sum(p.numel() for p in model.parameters())
        print(f"  模型参数数: {total_params:,}")

        # 4. 测试前向传播
        print("\n测试前向传播...")
        model.eval()

        # 获取一个批次
        batch_data, batch_labels = next(iter(train_loader))
        print(f"  批次数据形状: {batch_data.shape}")
        print(f"  批次标签形状: {batch_labels.shape}")

        # 前向传播
        with torch.no_grad():
            output = model(batch_data)

        print(f"  模型输出形状: {output.shape}")
        print("✓ 前向传播成功")

        # 5. 测试损失计算
        print("\n测试损失计算...")
        criterion = nn.CrossEntropyLoss()
        loss = criterion(output, batch_labels)
        print(f"  损失值: {loss.item():.6f}")
        print("✓ 损失计算成功")

        # 6. 测试优化器
        print("\n测试优化器...")
        optimizer = torch.optim.AdamW(
            model.parameters(),
            lr=config['training']['learning_rate'],
            weight_decay=config['training']['weight_decay']
        )
        print("✓ 优化器创建成功")

        # 7. 测试一步训练
        print("\n测试一步训练...")
        model.train()
        optimizer.zero_grad()

        output = model(batch_data)
        loss = criterion(output, batch_labels)
        loss.backward()
        optimizer.step()

        print(f"  训练后损失: {loss.item():.6f}")
        print("✓ 一步训练成功")

        print("\n" + "="*60)
        print("🎉 训练启动测试完全成功！")
        print("✅ 3通道多模态训练系统完全就绪")
        print("="*60)

        return True

    except Exception as e:
        print(f"\n❌ 训练启动测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始3通道多模态训练启动测试...")

    success = test_training_initialization()

    if success:
        print("\n🚀 系统完全就绪，可以开始正式训练！")
        print("\n建议的训练命令:")
        print("1. 1:1平衡数据集:")
        print("   python pulsar_trainer/train.py --config config/coatnet_config.yaml")
        print("2. 1:5不平衡数据集:")
        print("   python pulsar_trainer/train.py --config config/coatnet_config_1_5.yaml")
        print("3. 1:8不平衡数据集:")
        print("   python pulsar_trainer/train.py --config config/coatnet_config_1_8.yaml")
        return True
    else:
        print("\n❌ 系统未就绪，需要修复问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
