"""
物理约束频域注意力模块 (PC-AFDA)
Physics-Constrained Adaptive Frequency Domain Attention
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
from typing import Dict, Any
from .afda_base import AFDAModule


class PhysicsConstrainedAFDA(AFDAModule):
    """物理约束频域注意力实现"""
    
    def __init__(self, 
                 dispersion_weight: float = 0.4,
                 periodicity_weight: float = 0.6,
                 physical_constraints: Dict[str, Any] = None,
                 **kwargs):
        super().__init__(**kwargs)
        
        self.dispersion_weight = dispersion_weight
        self.periodicity_weight = periodicity_weight
        
        # 物理约束参数
        if physical_constraints is None:
            physical_constraints = {
                "dispersion": {"enabled": True, "weight": dispersion_weight},
                "periodicity": {"enabled": True, "weight": periodicity_weight}
            }
        self.physical_constraints = physical_constraints
        
        # 色散关系参数 (t_delay ∝ f^(-2))
        self.dispersion_enabled = physical_constraints.get("dispersion", {}).get("enabled", True)
        if self.dispersion_enabled:
            self.dispersion_coeff = nn.Parameter(torch.tensor(1.0))
            self.dispersion_mlp = nn.Sequential(
                nn.Linear(self.feature_dim, self.feature_dim // 2),
                nn.GELU(),
                nn.Linear(self.feature_dim // 2, self.frequency_bins),
                nn.Sigmoid()
            )
        
        # 周期性约束参数
        self.periodicity_enabled = physical_constraints.get("periodicity", {}).get("enabled", True)
        if self.periodicity_enabled:
            self.period_detector = nn.Sequential(
                nn.Linear(self.feature_dim, self.feature_dim // 2),
                nn.GELU(),
                nn.Linear(self.feature_dim // 2, self.frequency_bins),
                nn.Softmax(dim=-1)
            )
            
            # 周期性增强权重
            self.periodicity_enhancer = nn.Parameter(torch.ones(self.frequency_bins))
        
        # 物理约束融合网络
        self.constraint_fusion = nn.Sequential(
            nn.Linear(self.frequency_bins * 2, self.frequency_bins),
            nn.GELU(),
            nn.Linear(self.frequency_bins, self.frequency_bins),
            nn.Sigmoid()
        )
        
    def _apply_dispersion_constraint(self, freq_features: torch.Tensor, frequencies: torch.Tensor) -> torch.Tensor:
        """应用色散关系约束"""
        if not self.dispersion_enabled:
            return torch.ones_like(frequencies)
            
        B, freq_bins, D = freq_features.shape
        
        # 计算色散权重 (∝ f^(-2))
        # 避免零频率的问题
        freq_safe = frequencies + 1e-6
        dispersion_weights = 1.0 / (freq_safe ** 2)
        
        # 归一化权重
        dispersion_weights = dispersion_weights / dispersion_weights.sum(dim=-1, keepdim=True)
        
        # 通过MLP学习自适应色散模式
        global_repr = freq_features.mean(dim=1)  # (B, D)
        adaptive_dispersion = self.dispersion_mlp(global_repr)  # (B, freq_bins)
        
        # 结合理论色散权重和学习的自适应权重
        combined_weights = dispersion_weights * adaptive_dispersion * self.dispersion_coeff
        
        return combined_weights
        
    def _apply_periodicity_constraint(self, freq_features: torch.Tensor) -> torch.Tensor:
        """应用周期性约束"""
        if not self.periodicity_enabled:
            return torch.ones(freq_features.shape[0], self.frequency_bins, device=freq_features.device)
            
        B, freq_bins, D = freq_features.shape
        
        # 检测主要周期成分
        global_repr = freq_features.mean(dim=1)  # (B, D)
        period_weights = self.period_detector(global_repr)  # (B, freq_bins)
        
        # 增强周期性频率成分
        enhanced_weights = period_weights * self.periodicity_enhancer.unsqueeze(0)
        
        return enhanced_weights
        
    def _physics_constraints(self, freq_features: torch.Tensor) -> torch.Tensor:
        """应用物理约束"""
        B, attention_heads, freq_bins, head_dim = freq_features.shape
        
        # 创建频率轴
        frequencies = torch.linspace(0, 1, freq_bins, device=freq_features.device)
        frequencies = frequencies.unsqueeze(0).expand(B, -1)  # (B, freq_bins)
        
        # 将多头特征平均用于物理约束计算
        avg_features = freq_features.mean(dim=1)  # (B, freq_bins, head_dim)
        avg_features_flat = avg_features.mean(dim=-1)  # (B, freq_bins)
        
        # 应用色散约束
        dispersion_weights = self._apply_dispersion_constraint(
            avg_features, frequencies
        )  # (B, freq_bins)
        
        # 应用周期性约束
        periodicity_weights = self._apply_periodicity_constraint(
            avg_features
        )  # (B, freq_bins)
        
        # 融合物理约束
        constraint_input = torch.cat([
            dispersion_weights, periodicity_weights
        ], dim=-1)  # (B, freq_bins * 2)
        
        physics_weights = self.constraint_fusion(constraint_input)  # (B, freq_bins)
        
        # 扩展到多头维度
        physics_weights = physics_weights.unsqueeze(1).unsqueeze(-1)  # (B, 1, freq_bins, 1)
        physics_weights = physics_weights.expand(-1, attention_heads, -1, -1)
        
        return physics_weights
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """物理约束AFDA前向传播"""
        original_shape = x.shape
        B, C, H, W = x.shape
        
        # 1. 预归一化
        x_flat = x.view(B, C, H * W).permute(0, 2, 1)  # (B, H*W, C)
        x_norm = self.pre_norm(x_flat)
        
        # 2. FFT变换
        x_heads = x_norm.view(B, H*W, self.attention_heads, self.head_dim).permute(0, 2, 1, 3)
        F_fft = torch.fft.rfft(x_heads, dim=2, norm='ortho')
        
        # 3. 计算基础注意力权重
        adaptive_params = self.adaptive_mlp(x_norm.mean(dim=1))
        adaptive_params = adaptive_params.view(B, self.attention_heads, self.frequency_bins, 2)
        adaptive_scale = adaptive_params[..., 0:1]
        adaptive_bias = adaptive_params[..., 1:2]
        
        # 4. 应用物理约束
        physics_weights = self._physics_constraints(F_fft)
        
        # 5. 结合基础权重和物理约束
        base_filter = self.base_filter * (1 + adaptive_scale)
        physics_enhanced_filter = base_filter * physics_weights
        
        effective_filter = (
            self.dispersion_weight * physics_enhanced_filter + 
            (1 - self.dispersion_weight) * base_filter
        )
        effective_bias = self.base_bias + adaptive_bias
        
        # 6. 频域特征增强
        F_fft_mod = F_fft * effective_filter + effective_bias
        F_fft_nl = self.complex_activation(F_fft_mod)
        
        # 7. IFFT逆变换
        x_filtered = torch.fft.irfft(F_fft_nl, dim=2, n=H*W, norm='ortho')
        x_filtered = x_filtered.permute(0, 2, 1, 3).reshape(B, H*W, C)
        
        # 8. 残差连接
        output = x_norm + self.dropout(x_filtered)
        output = output.permute(0, 2, 1).view(B, C, H, W)
        
        return output
