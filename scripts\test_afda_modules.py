#!/usr/bin/env python3
"""
AFDA模块单元测试脚本
测试所有AFDA模块的功能：FFT变换、注意力计算、物理约束、混合架构
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import torch.nn as nn
import numpy as np
from pathlib import Path

from pulsar_trainer.models.afda import AFDAModule, MultiScaleAFDA, PhysicsConstrainedAFDA, HybridAFDA


def test_afda_base_module():
    """测试AFDA基础模块"""
    print("\n" + "="*60)
    print("测试AFDA基础模块")
    print("="*60)
    
    try:
        # 创建AFDA基础模块
        afda = AFDAModule(
            input_channels=3,
            feature_dim=256,
            frequency_bins=33,
            attention_heads=8,
            dropout=0.1
        )
        
        print("✓ AFDA基础模块创建成功")
        print(f"  参数数量: {sum(p.numel() for p in afda.parameters()):,}")
        
        # 测试前向传播
        test_input = torch.randn(4, 3, 64, 64)
        print(f"  输入形状: {test_input.shape}")
        
        with torch.no_grad():
            output = afda(test_input)
        
        print(f"  输出形状: {output.shape}")
        print("✓ 前向传播测试通过")
        
        # 测试梯度计算
        afda.train()
        test_input.requires_grad_(True)
        output = afda(test_input)
        loss = output.mean()
        loss.backward()
        
        print("✓ 梯度计算测试通过")
        
        # 测试FFT变换
        fft_result = afda._fft_transform(test_input)
        print(f"  FFT结果形状: {fft_result.shape}")
        print("✓ FFT变换测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ AFDA基础模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_multi_scale_afda():
    """测试多尺度AFDA模块"""
    print("\n" + "="*60)
    print("测试多尺度AFDA模块")
    print("="*60)
    
    try:
        # 创建多尺度AFDA模块
        ms_afda = MultiScaleAFDA(
            scales=[16, 32, 64],
            fusion_strategy="adaptive_weighted",
            input_channels=3,
            feature_dim=256,
            frequency_bins=33,
            attention_heads=8
        )
        
        print("✓ 多尺度AFDA模块创建成功")
        print(f"  参数数量: {sum(p.numel() for p in ms_afda.parameters()):,}")
        print(f"  尺度配置: {ms_afda.scales}")
        
        # 测试前向传播
        test_input = torch.randn(4, 3, 64, 64)
        
        with torch.no_grad():
            output = ms_afda(test_input)
        
        print(f"  输入形状: {test_input.shape}")
        print(f"  输出形状: {output.shape}")
        print("✓ 多尺度前向传播测试通过")
        
        # 测试多尺度FFT
        multi_scale_fft = ms_afda._multi_scale_fft(test_input)
        print(f"  多尺度FFT结果数量: {len(multi_scale_fft)}")
        for i, fft_result in enumerate(multi_scale_fft):
            print(f"    尺度{ms_afda.scales[i]}: {fft_result.shape}")
        print("✓ 多尺度FFT测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 多尺度AFDA模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_physics_constrained_afda():
    """测试物理约束AFDA模块"""
    print("\n" + "="*60)
    print("测试物理约束AFDA模块")
    print("="*60)
    
    try:
        # 创建物理约束AFDA模块
        pc_afda = PhysicsConstrainedAFDA(
            dispersion_weight=0.4,
            periodicity_weight=0.6,
            input_channels=3,
            feature_dim=256,
            frequency_bins=33,
            attention_heads=8
        )
        
        print("✓ 物理约束AFDA模块创建成功")
        print(f"  参数数量: {sum(p.numel() for p in pc_afda.parameters()):,}")
        print(f"  色散权重: {pc_afda.dispersion_weight}")
        print(f"  周期性权重: {pc_afda.periodicity_weight}")
        
        # 测试前向传播
        test_input = torch.randn(4, 3, 64, 64)
        
        with torch.no_grad():
            output = pc_afda(test_input)
        
        print(f"  输入形状: {test_input.shape}")
        print(f"  输出形状: {output.shape}")
        print("✓ 物理约束前向传播测试通过")
        
        # 测试物理约束计算
        x_heads = test_input.view(4, 3, 64*64).permute(0, 2, 1)
        x_heads = x_heads.view(4, 64*64, 8, 32).permute(0, 2, 1, 3)
        F_fft = torch.fft.rfft(x_heads, dim=2, norm='ortho')
        
        physics_weights = pc_afda._physics_constraints(F_fft)
        print(f"  物理约束权重形状: {physics_weights.shape}")
        print("✓ 物理约束计算测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 物理约束AFDA模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_hybrid_afda():
    """测试混合AFDA模块"""
    print("\n" + "="*60)
    print("测试混合AFDA模块")
    print("="*60)
    
    try:
        # 创建混合AFDA模块
        hybrid_afda = HybridAFDA(
            scales=[16, 32, 64],
            dispersion_weight=0.4,
            periodicity_weight=0.6,
            fusion_strategy="adaptive_weighted",
            input_channels=3,
            feature_dim=256,
            frequency_bins=33,
            attention_heads=8
        )
        
        print("✓ 混合AFDA模块创建成功")
        print(f"  参数数量: {sum(p.numel() for p in hybrid_afda.parameters()):,}")
        print(f"  多尺度配置: {hybrid_afda.scales}")
        print(f"  物理约束: 色散={hybrid_afda.dispersion_weight}, 周期性={hybrid_afda.periodicity_weight}")
        
        # 测试前向传播
        test_input = torch.randn(4, 3, 64, 64)
        
        with torch.no_grad():
            output = hybrid_afda(test_input)
        
        print(f"  输入形状: {test_input.shape}")
        print(f"  输出形状: {output.shape}")
        print("✓ 混合架构前向传播测试通过")
        
        # 测试内存使用
        if torch.cuda.is_available():
            hybrid_afda.cuda()
            test_input_gpu = test_input.cuda()
            
            torch.cuda.empty_cache()
            memory_before = torch.cuda.memory_allocated()
            
            with torch.no_grad():
                output_gpu = hybrid_afda(test_input_gpu)
            
            memory_after = torch.cuda.memory_allocated()
            memory_used = (memory_after - memory_before) / 1024 / 1024  # MB
            
            print(f"  GPU内存使用: {memory_used:.2f} MB")
            print("✓ GPU内存测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 混合AFDA模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_afda_compatibility():
    """测试AFDA模块兼容性"""
    print("\n" + "="*60)
    print("测试AFDA模块兼容性")
    print("="*60)
    
    try:
        # 测试不同输入尺寸
        input_sizes = [(2, 3, 64, 64), (8, 3, 64, 64), (16, 3, 64, 64)]
        
        hybrid_afda = HybridAFDA(
            input_channels=3,
            feature_dim=256,
            frequency_bins=33,
            attention_heads=8
        )
        
        for batch_size, channels, height, width in input_sizes:
            test_input = torch.randn(batch_size, channels, height, width)
            
            with torch.no_grad():
                output = hybrid_afda(test_input)
            
            print(f"  输入 {test_input.shape} -> 输出 {output.shape} ✓")
        
        print("✓ 不同批次大小兼容性测试通过")
        
        # 测试数值稳定性
        test_input = torch.randn(4, 3, 64, 64) * 10  # 大数值输入
        
        with torch.no_grad():
            output = hybrid_afda(test_input)
        
        if torch.isnan(output).any() or torch.isinf(output).any():
            print("❌ 数值稳定性测试失败：输出包含NaN或Inf")
            return False
        
        print("✓ 数值稳定性测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ AFDA兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("开始AFDA模块单元测试...")
    
    tests = [
        ("AFDA基础模块", test_afda_base_module),
        ("多尺度AFDA模块", test_multi_scale_afda),
        ("物理约束AFDA模块", test_physics_constrained_afda),
        ("混合AFDA模块", test_hybrid_afda),
        ("AFDA兼容性", test_afda_compatibility)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 执行测试: {test_name}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print(f"\n{'='*60}")
    print("AFDA模块单元测试结果汇总")
    print(f"{'='*60}")
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有AFDA模块测试通过！")
        print("✅ FFT变换、注意力计算、物理约束、混合架构全部正常")
        return True
    else:
        print("❌ 部分AFDA模块测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
