#!/usr/bin/env python3
"""
Real PHCX Data Extractor
Extract FPP/TPP data from real PHCX files and rebuild high-quality dataset
"""

import os
import sys
import glob
import numpy as np
import logging
import time
import json
import shutil
from pathlib import Path
from scipy import ndimage
from multiprocessing import Pool, cpu_count
import random

# Add current directory to path for phcx module
sys.path.append(os.path.dirname(__file__))

try:
    from phcx import Candidate
    PHCX_AVAILABLE = True
except ImportError:
    print("ERROR: phcx module not found!")
    PHCX_AVAILABLE = False
    sys.exit(1)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('D:/PulsarSystem/extraction.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class RealPHCXExtractor:
    """Extract FPP/TPP data from real PHCX files"""

    def __init__(self, target_size=64, output_dir="D:/pulsarSuanfa/datasets", fusion_mode="early"):
        self.target_size = target_size
        self.output_dir = Path(output_dir)
        self.fusion_mode = fusion_mode
        self.checkpoint_file = Path("D:/pulsarSuanfa/extraction_checkpoint.json")

        # Statistics
        self.stats = {
            'total_files': 0,
            'processed_files': 0,
            'failed_files': 0,
            'positive_samples': 0,
            'negative_samples': 0,
            'start_time': None,
            'failed_list': []
        }

    def verify_data_sources(self):
        """Verify all data source directories and count PHCX files"""
        logger.info("=" * 80)
        logger.info("VERIFYING DATA SOURCES")
        logger.info("=" * 80)

        # Define source directories
        source_dirs = {
            'pulsars': 'D:/pulsarSuanfa/HTRU/pulsars',
            'negatives_20K': 'D:/pulsarSuanfa/HTRU/negatives_20K',
            'RFI': 'D:/pulsarSuanfa/HTRU/RFI'
        }

        total_files = 0
        source_stats = {}

        for name, directory in source_dirs.items():
            if os.path.exists(directory):
                phcx_files = glob.glob(os.path.join(directory, "*.phcx"))
                file_count = len(phcx_files)
                total_files += file_count

                # Calculate total size
                total_size = sum(os.path.getsize(f) for f in phcx_files)
                total_size_mb = total_size / (1024 * 1024)

                source_stats[name] = {
                    'directory': directory,
                    'file_count': file_count,
                    'total_size_mb': total_size_mb,
                    'exists': True
                }

                logger.info(f"✅ {name}: {file_count} files ({total_size_mb:.1f} MB)")
            else:
                source_stats[name] = {
                    'directory': directory,
                    'file_count': 0,
                    'total_size_mb': 0,
                    'exists': False
                }
                logger.info(f"❌ {name}: Directory not found")

        logger.info(f"\nTotal PHCX files found: {total_files}")
        self.stats['total_files'] = total_files

        return source_stats

    def test_phcx_loading(self):
        """Test loading sample PHCX files to verify format"""
        logger.info("\n" + "=" * 80)
        logger.info("TESTING PHCX FILE LOADING")
        logger.info("=" * 80)

        # Test positive samples
        pulsar_dir = "D:/PulsarSystem/pulsars"
        test_files = []

        if os.path.exists(pulsar_dir):
            pulsar_files = glob.glob(os.path.join(pulsar_dir, "*.phcx"))[:3]
            test_files.extend([(f, 'positive') for f in pulsar_files])

        # Test negative samples
        neg_dir = "D:/PulsarSystem/RFI"
        if os.path.exists(neg_dir):
            neg_files = glob.glob(os.path.join(neg_dir, "*.phcx"))[:2]
            test_files.extend([(f, 'negative') for f in neg_files])

        logger.info(f"Testing {len(test_files)} sample files...")

        for i, (file_path, label) in enumerate(test_files):
            try:
                logger.info(f"\nTest {i+1}: {os.path.basename(file_path)} ({label})")

                cand = Candidate(file_path)

                logger.info(f"  Subbands shape: {cand.subbands.shape}")
                logger.info(f"  Subbands range: [{cand.subbands.min():.6f}, {cand.subbands.max():.6f}]")
                logger.info(f"  Subints shape: {cand.subints.shape}")
                logger.info(f"  Subints range: [{cand.subints.min():.6f}, {cand.subints.max():.6f}]")

                # Check for zero rows in original data
                subbands_zero_rows = []
                for row in range(cand.subbands.shape[0]):
                    if np.all(cand.subbands[row, :] == 0):
                        subbands_zero_rows.append(row)

                subints_zero_rows = []
                for row in range(cand.subints.shape[0]):
                    if np.all(cand.subints[row, :] == 0):
                        subints_zero_rows.append(row)

                logger.info(f"  Subbands zero rows: {subbands_zero_rows}")
                logger.info(f"  Subints zero rows: {subints_zero_rows}")
                logger.info(f"  ✅ File loaded successfully")

            except Exception as e:
                logger.error(f"  ❌ Error loading {file_path}: {e}")
                return False

        logger.info("\n✅ All test files loaded successfully!")
        return True

    def clean_existing_data(self):
        """Remove existing synthetic data and create fresh directory structure"""
        logger.info("\n" + "=" * 80)
        logger.info("CLEANING EXISTING DATASET DATA")
        logger.info("=" * 80)

        dataset_dir = self.output_dir

        if dataset_dir.exists():
            logger.info(f"Removing existing dataset directory: {dataset_dir}")
            shutil.rmtree(dataset_dir)

        # Create fresh directory structure for multimodal data
        directories = [
            dataset_dir / "MULTIMODAL" / "train",
            dataset_dir / "MULTIMODAL" / "validation",
            dataset_dir / "MULTIMODAL" / "test"
        ]

        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            logger.info(f"Created directory: {directory}")

        logger.info("✅ Directory structure created successfully")

    def collect_all_phcx_files(self):
        """Collect all PHCX files with labels"""
        logger.info("\n" + "=" * 80)
        logger.info("COLLECTING ALL PHCX FILES")
        logger.info("=" * 80)

        all_files = []

        # Positive samples (pulsars)
        pulsar_dir = "D:/pulsarSuanfa/HTRU/pulsars"
        if os.path.exists(pulsar_dir):
            pulsar_files = glob.glob(os.path.join(pulsar_dir, "*.phcx"))
            for file_path in pulsar_files:
                all_files.append({
                    'path': file_path,
                    'label': 1,  # positive
                    'source': 'pulsars',
                    'filename': os.path.basename(file_path)
                })
            logger.info(f"Found {len(pulsar_files)} positive samples")
            self.stats['positive_samples'] = len(pulsar_files)

        # Negative samples (negatives_20K and RFI)
        negative_dirs = [
            'D:/pulsarSuanfa/HTRU/negatives_20K',
            'D:/pulsarSuanfa/HTRU/RFI'
        ]
        negative_count = 0
        for neg_dir in negative_dirs:
            if os.path.exists(neg_dir):
                neg_files = glob.glob(os.path.join(neg_dir, "*.phcx"))
                source_name = os.path.basename(neg_dir)
                for file_path in neg_files:
                    all_files.append({
                        'path': file_path,
                        'label': 0,  # negative
                        'source': source_name,
                        'filename': os.path.basename(file_path)
                    })
                negative_count += len(neg_files)
                logger.info(f"Found {len(neg_files)} files in {source_name} directory")

        logger.info(f"Total negative samples: {negative_count}")
        self.stats['negative_samples'] = negative_count

        logger.info(f"Total files collected: {len(all_files)}")
        logger.info(f"Positive/Negative ratio: {self.stats['positive_samples']}/{negative_count}")

        return all_files

    def split_data(self, all_files):
        """Split data into train/validation/test sets with 1:1 balance"""
        logger.info("\n" + "=" * 80)
        logger.info("SPLITTING DATA INTO TRAIN/VALIDATION/TEST WITH 1:1 BALANCE")
        logger.info("=" * 80)

        # Set random seed for reproducibility
        random.seed(42)
        np.random.seed(42)

        # Separate positive and negative samples
        positive_files = [f for f in all_files if f['label'] == 1]
        negative_files = [f for f in all_files if f['label'] == 0]

        # Shuffle both lists
        random.shuffle(positive_files)
        random.shuffle(negative_files)

        logger.info(f"Available: {len(positive_files)} positive, {len(negative_files)} negative samples")

        # Define exact sample counts for 1:1 balance
        train_pos_count = 837
        train_neg_count = 837
        val_pos_count = 180
        val_neg_count = 180
        test_pos_count = 179
        test_neg_count = 179

        total_needed_pos = train_pos_count + val_pos_count + test_pos_count
        total_needed_neg = train_neg_count + val_neg_count + test_neg_count

        # Check if we have enough samples
        if len(positive_files) < total_needed_pos:
            logger.error(f"Not enough positive samples: need {total_needed_pos}, have {len(positive_files)}")
            raise ValueError("Insufficient positive samples")

        if len(negative_files) < total_needed_neg:
            logger.error(f"Not enough negative samples: need {total_needed_neg}, have {len(negative_files)}")
            raise ValueError("Insufficient negative samples")

        # Select exact number of samples for each split
        pos_train = positive_files[:train_pos_count]
        pos_val = positive_files[train_pos_count:train_pos_count + val_pos_count]
        pos_test = positive_files[train_pos_count + val_pos_count:train_pos_count + val_pos_count + test_pos_count]

        neg_train = negative_files[:train_neg_count]
        neg_val = negative_files[train_neg_count:train_neg_count + val_neg_count]
        neg_test = negative_files[train_neg_count + val_neg_count:train_neg_count + val_neg_count + test_neg_count]

        # Combine and create final splits
        splits = {
            'train': pos_train + neg_train,
            'validation': pos_val + neg_val,
            'test': pos_test + neg_test
        }

        # Shuffle each split
        for split_name in splits:
            random.shuffle(splits[split_name])

        # Log split statistics
        for split_name, files in splits.items():
            pos_count = sum(1 for f in files if f['label'] == 1)
            neg_count = sum(1 for f in files if f['label'] == 0)
            total_count = len(files)

            logger.info(f"{split_name}: {total_count} files ({pos_count} pos, {neg_count} neg, 1:1 balanced)")

        logger.info(f"✅ Perfect 1:1 balance achieved for all splits")
        return splits

    def create_multiple_ratio_datasets(self, all_files):
        """创建1:1、1:5、1:10三种比例数据集"""
        ratios = {
            "1_1": {"pos_ratio": 1, "neg_ratio": 1, "output_dir": "HTRU"},
            "1_5": {"pos_ratio": 1, "neg_ratio": 5, "output_dir": "HTRU_Unbalance_1_5"},
            "1_10": {"pos_ratio": 1, "neg_ratio": 10, "output_dir": "HTRU_Unbalance_1_10"}
        }

        datasets = {}
        for ratio_name, config in ratios.items():
            logger.info(f"\n创建 {ratio_name} 比例数据集...")
            datasets[ratio_name] = self.create_balanced_splits(
                all_files, config["pos_ratio"], config["neg_ratio"]
            )
        return datasets

    def create_balanced_splits(self, all_files, pos_ratio, neg_ratio):
        """创建指定比例的数据集分割"""
        import random

        # 分离正负样本
        positive_files = [f for f in all_files if f['label'] == 1]
        negative_files = [f for f in all_files if f['label'] == 0]

        # 计算所需样本数
        pos_count = len(positive_files)
        neg_count = int(pos_count * neg_ratio / pos_ratio)

        logger.info(f"目标比例 {pos_ratio}:{neg_ratio}, 正样本:{pos_count}, 需要负样本:{neg_count}")

        # 随机选择负样本
        if neg_count > len(negative_files):
            logger.warning(f"负样本不足，需要{neg_count}个，实际{len(negative_files)}个")
            selected_negatives = negative_files
            neg_count = len(negative_files)
        else:
            selected_negatives = random.sample(negative_files, neg_count)

        # 合并样本
        balanced_files = positive_files + selected_negatives
        random.shuffle(balanced_files)

        # 根据比例决定分割方法
        if pos_ratio == 1 and neg_ratio == 1:
            # 1:1平衡数据集使用原有方法
            return self.split_data(balanced_files)
        else:
            # 不平衡数据集使用新方法
            return self.split_imbalanced_data(balanced_files, pos_count, neg_count, pos_ratio, neg_ratio)

    def split_imbalanced_data(self, all_files, pos_count, neg_count, pos_ratio, neg_ratio):
        """Split data into train/validation/test sets with specified imbalance ratio"""
        logger.info("\n" + "=" * 80)
        logger.info(f"SPLITTING DATA INTO TRAIN/VALIDATION/TEST WITH {pos_ratio}:{neg_ratio} RATIO")
        logger.info("=" * 80)

        # Set random seed for reproducibility
        random.seed(42)
        np.random.seed(42)

        # Separate positive and negative samples
        positive_files = [f for f in all_files if f['label'] == 1]
        negative_files = [f for f in all_files if f['label'] == 0]

        # Shuffle both lists
        random.shuffle(positive_files)
        random.shuffle(negative_files)

        logger.info(f"Available: {len(positive_files)} positive, {len(negative_files)} negative samples")

        # Calculate split proportions (70% train, 15% validation, 15% test)
        train_ratio = 0.70
        val_ratio = 0.15
        test_ratio = 0.15

        # Calculate positive samples for each split
        train_pos_count = int(pos_count * train_ratio)
        val_pos_count = int(pos_count * val_ratio)
        test_pos_count = pos_count - train_pos_count - val_pos_count

        # Calculate negative samples for each split (maintaining ratio)
        train_neg_count = int(train_pos_count * neg_ratio / pos_ratio)
        val_neg_count = int(val_pos_count * neg_ratio / pos_ratio)
        test_neg_count = neg_count - train_neg_count - val_neg_count

        logger.info(f"Split计划:")
        logger.info(f"  train: {train_pos_count} pos + {train_neg_count} neg = {train_pos_count + train_neg_count}")
        logger.info(f"  validation: {val_pos_count} pos + {val_neg_count} neg = {val_pos_count + val_neg_count}")
        logger.info(f"  test: {test_pos_count} pos + {test_neg_count} neg = {test_pos_count + test_neg_count}")

        # Select samples for each split
        pos_train = positive_files[:train_pos_count]
        pos_val = positive_files[train_pos_count:train_pos_count + val_pos_count]
        pos_test = positive_files[train_pos_count + val_pos_count:train_pos_count + val_pos_count + test_pos_count]

        neg_train = negative_files[:train_neg_count]
        neg_val = negative_files[train_neg_count:train_neg_count + val_neg_count]
        neg_test = negative_files[train_neg_count + val_neg_count:train_neg_count + val_neg_count + test_neg_count]

        # Combine and create final splits
        splits = {
            'train': pos_train + neg_train,
            'validation': pos_val + neg_val,
            'test': pos_test + neg_test
        }

        # Shuffle each split
        for split_name in splits:
            random.shuffle(splits[split_name])

        # Log split statistics
        for split_name, files in splits.items():
            pos_count_split = sum(1 for f in files if f['label'] == 1)
            neg_count_split = sum(1 for f in files if f['label'] == 0)
            total_count = len(files)
            ratio_str = f"{pos_ratio}:{neg_ratio}" if pos_count_split > 0 else "0:N"

            logger.info(f"{split_name}: {total_count} files ({pos_count_split} pos, {neg_count_split} neg, {ratio_str} ratio)")

        logger.info(f"✅ {pos_ratio}:{neg_ratio} imbalanced splits created successfully")
        return splits

    def process_single_file(self, file_info):
        """Process a single PHCX file and extract FPP/TPP data"""
        try:
            file_path = file_info['path']
            label = file_info['label']
            source = file_info['source']
            filename = file_info['filename']
            split = file_info['split']

            # Load PHCX file
            cand = Candidate(file_path)

            # Extract FPP data (subbands) with zero row handling
            fpp_data = cand.subbands.copy()

            # Extract TPP data (subints)
            tpp_data = cand.subints.copy()

            # 生成3通道融合数据
            multimodal_data = self.create_multimodal_data(fpp_data, tpp_data)

            # 生成输出文件名
            base_name = filename.replace('.phcx', '')
            label_str = 'positive' if label == 1 else 'negative'
            multimodal_filename = f"{base_name}_{label_str}.npy"

            # 保存3通道数据
            multimodal_output_path = self.output_dir / "MULTIMODAL" / split / multimodal_filename
            # 确保目录存在
            multimodal_output_path.parent.mkdir(parents=True, exist_ok=True)
            np.save(multimodal_output_path, multimodal_data)

            return {
                'success': True,
                'file_path': file_path,
                'multimodal_shape': multimodal_data.shape,
                'multimodal_range': [multimodal_data.min(), multimodal_data.max()],
                'channel_ranges': {
                    'tpp': [multimodal_data[0].min(), multimodal_data[0].max()],
                    'fpp': [multimodal_data[1].min(), multimodal_data[1].max()],
                    'fusion': [multimodal_data[2].min(), multimodal_data[2].max()]
                }
            }

        except Exception as e:
            return {
                'success': False,
                'file_path': file_info['path'],
                'error': str(e)
            }

    def process_fpp_data(self, data):
        """Process FPP data with improved zero handling"""
        original_shape = data.shape

        # Find leading zero rows
        leading_zeros = 0
        for i in range(data.shape[0]):
            if not np.all(data[i, :] == 0):
                leading_zeros = i
                break

        # Find trailing zero rows
        trailing_zeros = 0
        for i in range(data.shape[0] - 1, -1, -1):
            if not np.all(data[i, :] == 0):
                trailing_zeros = data.shape[0] - 1 - i
                break

        # Calculate effective data region
        start_idx = leading_zeros
        end_idx = data.shape[0] - trailing_zeros

        # Safety check: ensure we keep at least 60% of original data
        min_rows = int(original_shape[0] * 0.6)
        if (end_idx - start_idx) < min_rows:
            logger.warning(f"Too many zero rows detected ({leading_zeros} leading, {trailing_zeros} trailing), using original data")
            data_cleaned = data.copy()
        else:
            data_cleaned = data[start_idx:end_idx, :].copy()
            if leading_zeros > 0 or trailing_zeros > 0:
                logger.debug(f"Removed {leading_zeros} leading and {trailing_zeros} trailing zero rows from FPP data")

        # Check for zero columns and handle them
        zero_cols = []
        for j in range(data_cleaned.shape[1]):
            if np.all(data_cleaned[:, j] == 0):
                zero_cols.append(j)

        if len(zero_cols) > 0:
            logger.debug(f"Found {len(zero_cols)} zero columns in FPP data")
            # Replace zero columns with small random noise to preserve structure
            for col in zero_cols:
                data_cleaned[:, col] = np.random.normal(0, 1e-6, data_cleaned.shape[0])

        # Process the cleaned data using standard method
        return self.process_data_array(data_cleaned)

    def process_data_array(self, data):
        """Process a 2D data array to target format"""
        # Resize to target size if needed
        if data.shape != (self.target_size, self.target_size):
            zoom_factors = (self.target_size / data.shape[0],
                          self.target_size / data.shape[1])
            data = ndimage.zoom(data, zoom_factors, order=1)

            # Ensure exact size
            if data.shape != (self.target_size, self.target_size):
                data = data[:self.target_size, :self.target_size]

        # Normalize to [0, 1]
        if data.max() > data.min():
            data = (data - data.min()) / (data.max() - data.min())
        else:
            # Handle constant data
            data = np.zeros_like(data)

        # Add channel dimension and convert to float32
        data_3d = data[:, :, np.newaxis].astype(np.float32)

        return data_3d

    def create_multimodal_data(self, fpp_data, tpp_data):
        """创建3通道融合数据：T + F + T×F"""
        # 处理FPP和TPP数据
        fpp_processed = self.process_data_array(fpp_data)  # (64, 64, 1)
        tpp_processed = self.process_data_array(tpp_data)  # (64, 64, 1)

        # 转换为2D格式进行融合计算
        fpp_2d = fpp_processed.squeeze()  # (64, 64)
        tpp_2d = tpp_processed.squeeze()  # (64, 64)

        # 计算融合通道（元素级乘法）
        fusion_2d = fpp_2d * tpp_2d  # (64, 64)

        # 堆叠为3通道 (3, 64, 64)
        multimodal_data = np.stack([tpp_2d, fpp_2d, fusion_2d], axis=0)
        return multimodal_data.astype(np.float32)

    def save_checkpoint(self, processed_files):
        """Save processing checkpoint"""
        checkpoint_data = {
            'processed_files': processed_files,
            'stats': self.stats,
            'timestamp': time.time()
        }

        with open(self.checkpoint_file, 'w') as f:
            json.dump(checkpoint_data, f, indent=2)

    def load_checkpoint(self):
        """Load processing checkpoint"""
        if self.checkpoint_file.exists():
            with open(self.checkpoint_file, 'r') as f:
                return json.load(f)
        return None

    def verify_output_structure(self):
        """Verify the output directory structure and file counts"""
        logger.info("\n" + "=" * 80)
        logger.info("VERIFYING OUTPUT STRUCTURE")
        logger.info("=" * 80)

        splits = ['train', 'validation', 'test']
        data_types = ['FPP', 'TPP']

        total_files = 0
        for data_type in data_types:
            logger.info(f"\n{data_type} Data:")
            for split in splits:
                split_dir = self.output_dir / data_type / split
                if split_dir.exists():
                    files = list(split_dir.glob("*.npy"))
                    file_count = len(files)
                    total_files += file_count

                    # Count positive and negative samples
                    pos_count = len([f for f in files if 'positive' in f.name])
                    neg_count = len([f for f in files if 'negative' in f.name])

                    logger.info(f"  {split}: {file_count} files ({pos_count} pos, {neg_count} neg)")

                    # Verify a sample file
                    if files:
                        sample_file = files[0]
                        try:
                            sample_data = np.load(sample_file)
                            logger.info(f"    Sample shape: {sample_data.shape}, dtype: {sample_data.dtype}")
                            logger.info(f"    Value range: [{sample_data.min():.6f}, {sample_data.max():.6f}]")
                        except Exception as e:
                            logger.error(f"    Error loading sample file: {e}")
                else:
                    logger.warning(f"  {split}: Directory not found")

        logger.info(f"\nTotal output files: {total_files}")
        logger.info("✅ Output structure verification complete")

def main():
    """Main extraction function"""
    logger.info("=" * 80)
    logger.info("REAL PHCX DATA EXTRACTION")
    logger.info("=" * 80)
    logger.info("Extracting FPP/TPP data from real PHCX files")
    logger.info("=" * 80)

    if not PHCX_AVAILABLE:
        logger.error("PHCX module not available. Cannot proceed.")
        return 1

    # Initialize extractor
    extractor = RealPHCXExtractor()
    extractor.stats['start_time'] = time.time()

    # Step 1: Verify data sources
    source_stats = extractor.verify_data_sources()

    if extractor.stats['total_files'] == 0:
        logger.error("No PHCX files found. Cannot proceed.")
        return 1

    # Step 2: Test PHCX loading
    if not extractor.test_phcx_loading():
        logger.error("PHCX loading test failed. Cannot proceed.")
        return 1

    # Step 3: Clean existing data
    extractor.clean_existing_data()

    # Step 4: Collect all files
    all_files = extractor.collect_all_phcx_files()

    # Step 5: Create multiple ratio datasets
    datasets = extractor.create_multiple_ratio_datasets(all_files)

    # Step 6: Process all datasets
    logger.info("\n" + "=" * 80)
    logger.info("STARTING MULTI-RATIO DATASET PROCESSING")
    logger.info("=" * 80)

    total_processed = 0
    total_failed = 0

    # Process each ratio dataset
    for ratio_name, splits in datasets.items():
        logger.info(f"\n{'='*60}")
        logger.info(f"PROCESSING {ratio_name.upper()} DATASET")
        logger.info(f"{'='*60}")

        # Update output directory for current ratio
        ratio_config = {
            "1_5": "HTRU_Unbalance_1_5",
            "1_10": "HTRU_Unbalance_1_10"
        }
        extractor.output_dir = Path(f"D:/pulsarSuanfa/datasets/{ratio_config[ratio_name]}")

        dataset_processed = 0
        dataset_failed = 0

        for split_name, files in splits.items():
            logger.info(f"\nProcessing {split_name} split: {len(files)} files")

            for i, file_info in enumerate(files):
                # Add split information to file_info
                file_info['split'] = split_name

                # Process the file
                result = extractor.process_single_file(file_info)

                if result['success']:
                    dataset_processed += 1
                    total_processed += 1
                    if (i + 1) % 50 == 0:  # Log progress every 50 files
                        logger.info(f"  Processed {i + 1}/{len(files)} files in {split_name}")
                else:
                    dataset_failed += 1
                    total_failed += 1
                    logger.error(f"  Failed to process {result['file_path']}: {result['error']}")

            logger.info(f"✅ Completed {split_name} split")

        logger.info(f"✅ {ratio_name} dataset complete: {dataset_processed} processed, {dataset_failed} failed")

    # Update final statistics
    extractor.stats['processed_files'] = total_processed
    extractor.stats['failed_files'] = total_failed
    end_time = time.time()
    processing_time = end_time - extractor.stats['start_time']

    # Final summary
    logger.info("\n" + "=" * 80)
    logger.info("MULTI-RATIO DATA EXTRACTION COMPLETE")
    logger.info("=" * 80)
    logger.info(f"Total files processed: {total_processed}")
    logger.info(f"Total failed files: {total_failed}")
    logger.info(f"Processing time: {processing_time:.2f} seconds")
    if total_processed > 0:
        logger.info(f"Average time per file: {processing_time/total_processed:.3f} seconds")

    if total_failed > 0:
        logger.warning(f"Some files failed processing")

    # Verify output structure for all datasets
    for ratio_name in datasets.keys():
        ratio_config = {
            "1_5": "HTRU_Unbalance_1_5",
            "1_10": "HTRU_Unbalance_1_10"
        }
        extractor.output_dir = Path(f"D:/pulsarSuanfa/datasets/{ratio_config[ratio_name]}")
        logger.info(f"\nVerifying {ratio_name} dataset structure...")
        extractor.verify_output_structure()

    logger.info("✅ Data extraction completed successfully!")
    return 0

if __name__ == "__main__":
    exit(main())
