#!/usr/bin/env python3
"""
AFDA集成测试脚本
测试AFDA与CoAtNet的集成：模型创建、前向传播、梯度计算、内存使用
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import torch.nn as nn
import numpy as np
from pathlib import Path

from pulsar_trainer.models.coatnet import CoAtNet, create_coatnet_from_config
from pulsar_trainer.utils.config import ModelConfig, AFDAConfig


def test_coatnet_afda_creation():
    """测试CoAtNet与AFDA集成的模型创建"""
    print("\n" + "="*60)
    print("测试CoAtNet与AFDA集成的模型创建")
    print("="*60)
    
    try:
        # 创建AFDA配置
        afda_config = AFDAConfig(
            enabled=True,
            architecture="ms_pc_hybrid",
            multi_scale={
                "scales": [16, 32, 64],
                "attention_heads": [4, 8, 12],
                "fusion_strategy": "adaptive_weighted"
            },
            physical_constraints={
                "dispersion": {"enabled": True, "weight": 0.4},
                "periodicity": {"enabled": True, "weight": 0.6}
            },
            adaptive_fusion={
                "threshold": 0.5,
                "temperature": 1.0,
                "dropout": 0.1
            }
        )
        
        # 创建模型配置
        model_config = ModelConfig(
            num_blocks=[2, 2, 6, 8, 2],
            channels=[96, 128, 256, 512, 1024],
            block_types=['C', 'C', 'T', 'T'],
            image_size=[64, 64],
            in_channels=3,
            num_classes=2,
            dropout=0.2
        )
        model_config.afda = afda_config
        
        # 创建集成模型
        model = create_coatnet_from_config(model_config)
        
        print("✓ CoAtNet + AFDA模型创建成功")
        
        # 检查AFDA模块是否正确集成
        if hasattr(model, 'afda_module') and model.afda_module is not None:
            print("✓ AFDA模块正确集成到CoAtNet中")
            afda_params = sum(p.numel() for p in model.afda_module.parameters())
            print(f"  AFDA模块参数数: {afda_params:,}")
        else:
            print("❌ AFDA模块未正确集成")
            return False
        
        # 检查总参数数
        total_params = sum(p.numel() for p in model.parameters())
        print(f"  模型总参数数: {total_params:,}")
        
        return True
        
    except Exception as e:
        print(f"❌ CoAtNet + AFDA模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_integrated_forward_pass():
    """测试集成模型的前向传播"""
    print("\n" + "="*60)
    print("测试集成模型的前向传播")
    print("="*60)
    
    try:
        # 创建集成模型
        afda_config = AFDAConfig(enabled=True)
        model_config = ModelConfig(in_channels=3, num_classes=2)
        model_config.afda = afda_config
        
        model = create_coatnet_from_config(model_config)
        model.eval()
        
        # 测试前向传播
        test_input = torch.randn(4, 3, 64, 64)
        print(f"  输入形状: {test_input.shape}")
        
        with torch.no_grad():
            output = model(test_input)
        
        print(f"  输出形状: {output.shape}")
        print(f"  输出范围: [{output.min().item():.4f}, {output.max().item():.4f}]")
        
        # 检查输出维度
        if output.shape == (4, 2):
            print("✓ 输出维度正确")
        else:
            print(f"❌ 输出维度错误，期望(4, 2)，实际{output.shape}")
            return False
        
        # 检查数值稳定性
        if torch.isnan(output).any() or torch.isinf(output).any():
            print("❌ 输出包含NaN或Inf")
            return False
        
        print("✓ 前向传播测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 集成前向传播测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_gradient_computation():
    """测试梯度计算"""
    print("\n" + "="*60)
    print("测试梯度计算")
    print("="*60)
    
    try:
        # 创建集成模型
        afda_config = AFDAConfig(enabled=True)
        model_config = ModelConfig(in_channels=3, num_classes=2)
        model_config.afda = afda_config
        
        model = create_coatnet_from_config(model_config)
        model.train()
        
        # 创建优化器
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        criterion = nn.CrossEntropyLoss()
        
        # 测试梯度计算
        test_input = torch.randn(4, 3, 64, 64)
        test_target = torch.randint(0, 2, (4,))
        
        optimizer.zero_grad()
        output = model(test_input)
        loss = criterion(output, test_target)
        loss.backward()
        
        print(f"  损失值: {loss.item():.4f}")
        
        # 检查梯度
        grad_norms = []
        for name, param in model.named_parameters():
            if param.grad is not None:
                grad_norm = param.grad.norm().item()
                grad_norms.append(grad_norm)
                if 'afda' in name:
                    print(f"  AFDA参数 {name}: 梯度范数 {grad_norm:.6f}")
        
        if len(grad_norms) == 0:
            print("❌ 没有计算到梯度")
            return False
        
        avg_grad_norm = np.mean(grad_norms)
        print(f"  平均梯度范数: {avg_grad_norm:.6f}")
        
        # 检查梯度是否合理
        if avg_grad_norm > 100 or avg_grad_norm < 1e-8:
            print(f"❌ 梯度范数异常: {avg_grad_norm}")
            return False
        
        print("✓ 梯度计算测试通过")
        
        # 测试优化器步骤
        optimizer.step()
        print("✓ 优化器更新测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 梯度计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_memory_usage():
    """测试内存使用"""
    print("\n" + "="*60)
    print("测试内存使用")
    print("="*60)
    
    try:
        # 测试CPU内存
        import psutil
        import gc
        
        process = psutil.Process()
        memory_before = process.memory_info().rss / 1024 / 1024  # MB
        
        # 创建集成模型
        afda_config = AFDAConfig(enabled=True)
        model_config = ModelConfig(in_channels=3, num_classes=2)
        model_config.afda = afda_config
        
        model = create_coatnet_from_config(model_config)
        
        memory_after = process.memory_info().rss / 1024 / 1024  # MB
        memory_used = memory_after - memory_before
        
        print(f"  CPU内存使用: {memory_used:.2f} MB")
        
        # 测试GPU内存（如果可用）
        if torch.cuda.is_available():
            model.cuda()
            torch.cuda.empty_cache()
            
            memory_before_gpu = torch.cuda.memory_allocated() / 1024 / 1024  # MB
            
            test_input = torch.randn(8, 3, 64, 64).cuda()
            with torch.no_grad():
                output = model(test_input)
            
            memory_after_gpu = torch.cuda.memory_allocated() / 1024 / 1024  # MB
            gpu_memory_used = memory_after_gpu - memory_before_gpu
            
            print(f"  GPU内存使用: {gpu_memory_used:.2f} MB")
            
            # 检查内存使用是否在合理范围内（<2GB）
            if gpu_memory_used > 2048:
                print(f"❌ GPU内存使用过高: {gpu_memory_used:.2f} MB")
                return False
            
            print("✓ GPU内存使用测试通过")
        
        print("✓ 内存使用测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 内存使用测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_model_save_load():
    """测试模型保存和加载"""
    print("\n" + "="*60)
    print("测试模型保存和加载")
    print("="*60)
    
    try:
        # 创建集成模型
        afda_config = AFDAConfig(enabled=True)
        model_config = ModelConfig(in_channels=3, num_classes=2)
        model_config.afda = afda_config
        
        model = create_coatnet_from_config(model_config)
        
        # 保存模型
        save_path = "temp_afda_model.pth"
        torch.save(model.state_dict(), save_path)
        print("✓ 模型保存成功")
        
        # 创建新模型并加载权重
        model_new = create_coatnet_from_config(model_config)
        model_new.load_state_dict(torch.load(save_path))
        print("✓ 模型加载成功")
        
        # 测试加载后的模型
        test_input = torch.randn(2, 3, 64, 64)
        
        model.eval()
        model_new.eval()
        
        with torch.no_grad():
            output1 = model(test_input)
            output2 = model_new(test_input)
        
        # 检查输出是否一致
        if torch.allclose(output1, output2, atol=1e-6):
            print("✓ 保存/加载后输出一致")
        else:
            print("❌ 保存/加载后输出不一致")
            return False
        
        # 清理临时文件
        os.remove(save_path)
        print("✓ 临时文件清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型保存/加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("开始AFDA集成测试...")
    
    tests = [
        ("CoAtNet + AFDA模型创建", test_coatnet_afda_creation),
        ("集成前向传播", test_integrated_forward_pass),
        ("梯度计算", test_gradient_computation),
        ("内存使用", test_memory_usage),
        ("模型保存/加载", test_model_save_load)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 执行测试: {test_name}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print(f"\n{'='*60}")
    print("AFDA集成测试结果汇总")
    print(f"{'='*60}")
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有AFDA集成测试通过！")
        print("✅ 模型创建、前向传播、梯度计算、内存使用全部正常")
        return True
    else:
        print("❌ 部分AFDA集成测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
