# CoAtNet配置文件 - 1:5不平衡数据集
# 基于coatnet_config.yaml，针对1:5不平衡数据集优化

# 数据配置
data:
  data_root: "D:/pulsarSuanfa/datasets/HTRU_Unbalance_1_5"
  modality: "MULTIMODAL"  # 3通道多模态数据
  normalize: true
  batch_size: 32
  num_workers: 4
  pin_memory: true

# 模型配置
model:
  type: "coatnet"
  coatnet:
    # 网络架构参数
    image_size: [64, 64]
    num_classes: 2
    channels: [96, 128, 256, 512, 1024]
    num_blocks: [2, 2, 6, 8, 2]
    block_types: ['C', 'C', 'T', 'T']
    in_channels: 3  # 3通道多模态输入 (T+F+T×F)
    dropout: 0.1

# 数据增强配置
augmentation:
  enabled: true  # 主控开关

  # 相位偏移
  phase_shift:
    enabled: true
    probability: 0.3
    max_shift: 0.1

  # 频率偏移（仅FPP）
  frequency_shift:
    enabled: true
    probability: 0.2
    max_shift: 0.05

  # 时间偏移（仅TPP）
  time_shift:
    enabled: true
    probability: 0.2
    max_shift: 0.05

  # 亮度缩放
  brightness_scaling:
    enabled: true
    probability: 0.4
    scale_range: [0.8, 1.2]

  # 噪声添加
  noise:
    enabled: true
    probability: 0.3
    noise_level: 0.02

# 训练配置
training:
  batch_size: 32
  epochs: 100
  learning_rate: 0.001
  weight_decay: 0.01
  optimizer: "adamw"

  # 损失函数配置 - 针对1:5不平衡数据集使用焦点损失
  loss:
    type: "focal"
    alpha: 2.0  # 对正样本加权
    gamma: 2.0  # 聚焦参数

  # 学习率调度
  scheduler:
    type: "cosine"
    warmup_epochs: 10
    min_lr: 1e-6

  # 早停配置
  early_stopping:
    enabled: true
    patience: 15
    min_delta: 0.001
    monitor: "val_f1"
    mode: "max"

# 验证配置
validation:
  frequency: 1  # 每个epoch验证一次
  metrics: ["accuracy", "precision", "recall", "f1", "auc"]
  save_best: true
  monitor_metric: "f1"

# 日志配置
logging:
  level: "INFO"
  save_logs: true
  log_dir: "logs"
  tensorboard: true

# 模型保存配置
checkpoint:
  save_dir: "checkpoints/coatnet_1_5"
  save_best: true
  save_last: true
  monitor: "val_f1"
  mode: "max"
