# CoAtNet Pulsar Classification Configuration - 1:5 Unbalanced Dataset
# Version 3.0.0 - Based on working coatnet_config.yaml
# Optimized for 1:5 imbalanced dataset with focal loss

# 项目信息
project:
  name: "pulsar-coatnet-1-5"
  version: "3.0.0"
  description: "CoAtNet-based pulsar classification for 1:5 imbalanced dataset"

# 数据配置
data:
  modality: "MULTIMODAL"  # 3通道多模态数据
  data_root: "D:/pulsarSuanfa/datasets/HTRU_Unbalance_1_5"
  normalize: true
  num_workers: 4
  pin_memory: true

# 模型配置（与基础配置完全一致）
model:
  name: "coatnet"
  coatnet:
    num_blocks: [2, 2, 6, 8, 2]
    channels: [96, 128, 256, 512, 1024]
    block_types: ['C', 'C', 'T', 'T']
    image_size: [64, 64]
    in_channels: 3  # 3通道多模态输入 (T+F+T×F)
    num_classes: 2
    dropout: 0.2

# 数据增强配置（与基础配置完全一致）
augmentation:
  enabled: true  # 主控开关
  phase_shift:
    enabled: true
    probability: 0.7
    max_shift: 1.0
  frequency_shift:
    enabled: true
    probability: 0.3
    max_shift: 0.1
  time_shift:
    enabled: true
    probability: 0.3
    max_shift: 0.1
  brightness_scaling:
    enabled: true
    probability: 0.5
    scale_range: [0.8, 1.2]
  noise:
    enabled: true
    probability: 0.4
    noise_level: 0.01

# 训练配置
training:
  batch_size: 32
  epochs: 100
  learning_rate: 0.001
  weight_decay: 0.01
  optimizer: "adamw"
  scheduler:
    type: "cosine"
    warmup_epochs: 10
    min_lr: 0.00001
  early_stopping:
    patience: 15
    min_delta: 0.001
  # 焦点损失配置 - 针对1:5不平衡数据集
  loss:
    type: "focal"
    alpha: 2.0  # 对正样本加权
    gamma: 2.0  # 聚焦参数

# 设备配置（与基础配置完全一致）
device:
  use_cuda: true
  device_id: 0
  compile_model: false
  benchmark: true
  deterministic: false

# 输出配置
output:
  base_dir: "outputs_1_5"  # 专门的输出目录
  save_best_model: true
  save_last_model: true
  save_optimizer: true
  log_interval: 10
  eval_interval: 1

# 日志配置（与基础配置完全一致）
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  save_to_file: true
  console_output: true

# 评估配置（与基础配置完全一致）
evaluation:
  metrics:
    - "accuracy"
    - "precision"
    - "recall"
    - "f1_score"
    - "auc_roc"
    - "confusion_matrix"
  save_predictions: true
  save_probabilities: true
  generate_plots: true
  misclassification_analysis: true

# 性能目标配置（与基础配置完全一致）
performance_targets:
  accuracy: 0.99
  precision: 0.99
  recall: 0.99
  f1_score: 0.99
