2025-08-01 11:23:52 - pulsar_trainer - INFO - <PERSON><PERSON> initialized. Log file: D:\pulsarSuanfa\outputs_MULTIMODAL\logs\pulsar_trainer_20250801_112352.log
2025-08-01 11:23:52 - pulsar_trainer - INFO - 随机种子设置为: 42
2025-08-01 11:23:52 - pulsar_trainer - INFO - 使用配置文件参数创建模型
2025-08-01 11:23:52 - pulsar_trainer - INFO - 模型配置 - num_blocks: [2, 2, 6, 8, 2]
2025-08-01 11:23:52 - pulsar_trainer - INFO - 模型配置 - channels: [96, 128, 256, 512, 1024]
2025-08-01 11:23:52 - pulsar_trainer - INFO - 模型配置 - in_channels: 3
2025-08-01 11:23:52 - pulsar_trainer - INFO - 模型架构详情:
2025-08-01 11:23:52 - pulsar_trainer - INFO -   总参数数量: 38,727,856
2025-08-01 11:23:52 - pulsar_trainer - INFO -   可训练参数: 38,727,856
2025-08-01 11:23:52 - pulsar_trainer - INFO -   模型大小: 147.74 MB
2025-08-01 11:23:52 - pulsar_trainer - INFO -   块类型配置: ['C', 'C', 'T', 'T']
2025-08-01 11:23:52 - pulsar_trainer - INFO -   各阶段块数: [2, 2, 6, 8, 2]
2025-08-01 11:23:52 - pulsar_trainer - INFO -   各阶段通道数: [96, 128, 256, 512, 1024]
2025-08-01 11:23:52 - pulsar_trainer - INFO - Using standard cross entropy loss
2025-08-01 11:23:52 - pulsar_trainer - INFO - 🖥️ 使用GPU: NVIDIA GeForce RTX 4050 Laptop GPU
2025-08-01 11:23:52 - pulsar_trainer - INFO - 💾 GPU内存: 6.4GB
2025-08-01 11:23:52 - pulsar_trainer - INFO - 📋 模型参数总数: 38,727,856
2025-08-01 11:23:52 - pulsar_trainer - INFO - 🏗️ 模型架构: CoAtNet
2025-08-01 11:23:52 - pulsar_trainer - INFO - 📚 数据集大小:
2025-08-01 11:23:52 - pulsar_trainer - INFO -   - 训练集: 1674
2025-08-01 11:23:52 - pulsar_trainer - INFO -   - 验证集: 360
2025-08-01 11:23:52 - pulsar_trainer - INFO -   - 测试集: 358
2025-08-01 11:23:52 - pulsar_trainer - INFO -   - 总计: 2392
2025-08-01 11:23:52 - pulsar_trainer - INFO - ============================================================
2025-08-01 11:23:52 - pulsar_trainer - INFO - 🚀 开始脉冲星分类训练
2025-08-01 11:23:52 - pulsar_trainer - INFO - 📊 模态: MULTIMODAL
2025-08-01 11:23:52 - pulsar_trainer - INFO - 🧠 模型: coatnet
2025-08-01 11:23:52 - pulsar_trainer - INFO - 📦 批次大小: 32
2025-08-01 11:23:52 - pulsar_trainer - INFO - 🎯 学习率: 0.001
2025-08-01 11:23:52 - pulsar_trainer - INFO - 🔄 训练轮数: 100
2025-08-01 11:23:52 - pulsar_trainer - INFO - ============================================================
2025-08-01 11:24:43 - pulsar_trainer - INFO - Epoch   1: Train Loss=4.1527, Val Loss=1.8346, Train Acc=80.65%, Val Acc=50.00%
2025-08-01 11:24:43 - pulsar_trainer - INFO - Validation F1: 33.3333, Precision: 25.0000, Recall: 50.0000
2025-08-01 11:24:43 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_MULTIMODAL\models\best_model.pth
2025-08-01 11:24:48 - pulsar_trainer - INFO - Epoch   2: Train Loss=0.3372, Val Loss=0.0855, Train Acc=90.50%, Val Acc=97.78%
2025-08-01 11:24:48 - pulsar_trainer - INFO - Validation F1: 97.7772, Precision: 97.8309, Recall: 97.7778
2025-08-01 11:24:48 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_MULTIMODAL\models\best_model.pth
2025-08-01 11:24:52 - pulsar_trainer - INFO - Epoch   3: Train Loss=0.1722, Val Loss=0.0902, Train Acc=94.27%, Val Acc=97.50%
2025-08-01 11:24:52 - pulsar_trainer - INFO - Validation F1: 97.4998, Precision: 97.5132, Recall: 97.5000
2025-08-01 11:24:57 - pulsar_trainer - INFO - Epoch   4: Train Loss=0.1344, Val Loss=0.0626, Train Acc=95.70%, Val Acc=98.06%
2025-08-01 11:24:57 - pulsar_trainer - INFO - Validation F1: 98.0554, Precision: 98.0689, Recall: 98.0556
2025-08-01 11:24:57 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_MULTIMODAL\models\best_model.pth
2025-08-01 11:25:02 - pulsar_trainer - INFO - Epoch   5: Train Loss=0.1306, Val Loss=0.0870, Train Acc=95.58%, Val Acc=97.50%
2025-08-01 11:25:02 - pulsar_trainer - INFO - Validation F1: 97.4984, Precision: 97.6190, Recall: 97.5000
2025-08-01 11:25:06 - pulsar_trainer - INFO - Epoch   6: Train Loss=0.0971, Val Loss=0.0478, Train Acc=96.54%, Val Acc=98.61%
2025-08-01 11:25:06 - pulsar_trainer - INFO - Validation F1: 98.6110, Precision: 98.6246, Recall: 98.6111
2025-08-01 11:25:07 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_MULTIMODAL\models\best_model.pth
2025-08-01 11:25:11 - pulsar_trainer - INFO - Epoch   7: Train Loss=0.0998, Val Loss=0.0463, Train Acc=97.07%, Val Acc=98.33%
2025-08-01 11:25:11 - pulsar_trainer - INFO - Validation F1: 98.3333, Precision: 98.3393, Recall: 98.3333
2025-08-01 11:25:16 - pulsar_trainer - INFO - Epoch   8: Train Loss=0.0768, Val Loss=0.0372, Train Acc=97.43%, Val Acc=99.17%
2025-08-01 11:25:16 - pulsar_trainer - INFO - Validation F1: 99.1667, Precision: 99.1682, Recall: 99.1667
2025-08-01 11:25:17 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_MULTIMODAL\models\best_model.pth
2025-08-01 11:25:21 - pulsar_trainer - INFO - Epoch   9: Train Loss=0.0828, Val Loss=0.0734, Train Acc=97.25%, Val Acc=97.50%
2025-08-01 11:25:21 - pulsar_trainer - INFO - Validation F1: 97.4995, Precision: 97.5367, Recall: 97.5000
2025-08-01 11:25:26 - pulsar_trainer - INFO - Epoch  10: Train Loss=0.0566, Val Loss=0.0393, Train Acc=98.15%, Val Acc=98.89%
2025-08-01 11:25:26 - pulsar_trainer - INFO - Validation F1: 98.8889, Precision: 98.8949, Recall: 98.8889
2025-08-01 11:25:30 - pulsar_trainer - INFO - Epoch  11: Train Loss=0.0637, Val Loss=0.0349, Train Acc=98.03%, Val Acc=99.17%
2025-08-01 11:25:30 - pulsar_trainer - INFO - Validation F1: 99.1667, Precision: 99.1682, Recall: 99.1667
2025-08-01 11:25:34 - pulsar_trainer - INFO - Epoch  12: Train Loss=0.0585, Val Loss=0.0361, Train Acc=98.03%, Val Acc=98.89%
2025-08-01 11:25:34 - pulsar_trainer - INFO - Validation F1: 98.8889, Precision: 98.8889, Recall: 98.8889
2025-08-01 11:25:39 - pulsar_trainer - INFO - Epoch  13: Train Loss=0.0519, Val Loss=0.0343, Train Acc=98.21%, Val Acc=98.61%
2025-08-01 11:25:39 - pulsar_trainer - INFO - Validation F1: 98.6111, Precision: 98.6126, Recall: 98.6111
2025-08-01 11:25:43 - pulsar_trainer - INFO - Epoch  14: Train Loss=0.0514, Val Loss=0.0685, Train Acc=98.33%, Val Acc=97.78%
2025-08-01 11:25:43 - pulsar_trainer - INFO - Validation F1: 97.7775, Precision: 97.8014, Recall: 97.7778
2025-08-01 11:25:48 - pulsar_trainer - INFO - Epoch  15: Train Loss=0.0395, Val Loss=0.0570, Train Acc=98.92%, Val Acc=98.61%
2025-08-01 11:25:48 - pulsar_trainer - INFO - Validation F1: 98.6108, Precision: 98.6486, Recall: 98.6111
2025-08-01 11:25:53 - pulsar_trainer - INFO - Epoch  16: Train Loss=0.0420, Val Loss=0.0299, Train Acc=98.51%, Val Acc=99.17%
2025-08-01 11:25:53 - pulsar_trainer - INFO - Validation F1: 99.1667, Precision: 99.1682, Recall: 99.1667
2025-08-01 11:25:57 - pulsar_trainer - INFO - Epoch  17: Train Loss=0.0374, Val Loss=0.0758, Train Acc=98.92%, Val Acc=97.22%
2025-08-01 11:25:57 - pulsar_trainer - INFO - Validation F1: 97.2215, Precision: 97.2747, Recall: 97.2222
2025-08-01 11:26:02 - pulsar_trainer - INFO - Epoch  18: Train Loss=0.0474, Val Loss=0.0484, Train Acc=98.21%, Val Acc=99.17%
2025-08-01 11:26:02 - pulsar_trainer - INFO - Validation F1: 99.1667, Precision: 99.1682, Recall: 99.1667
2025-08-01 11:26:06 - pulsar_trainer - INFO - Epoch  19: Train Loss=0.0488, Val Loss=0.0348, Train Acc=98.51%, Val Acc=98.89%
2025-08-01 11:26:06 - pulsar_trainer - INFO - Validation F1: 98.8889, Precision: 98.8949, Recall: 98.8889
2025-08-01 11:26:10 - pulsar_trainer - INFO - Epoch  20: Train Loss=0.0620, Val Loss=0.0346, Train Acc=97.79%, Val Acc=98.89%
2025-08-01 11:26:10 - pulsar_trainer - INFO - Validation F1: 98.8889, Precision: 98.8949, Recall: 98.8889
2025-08-01 11:26:15 - pulsar_trainer - INFO - Epoch  21: Train Loss=0.0445, Val Loss=0.0490, Train Acc=98.63%, Val Acc=98.61%
2025-08-01 11:26:15 - pulsar_trainer - INFO - Validation F1: 98.6111, Precision: 98.6126, Recall: 98.6111
2025-08-01 11:26:19 - pulsar_trainer - INFO - Epoch  22: Train Loss=0.0372, Val Loss=0.0441, Train Acc=98.75%, Val Acc=99.17%
2025-08-01 11:26:19 - pulsar_trainer - INFO - Validation F1: 99.1667, Precision: 99.1682, Recall: 99.1667
2025-08-01 11:26:24 - pulsar_trainer - INFO - Epoch  23: Train Loss=0.0392, Val Loss=0.0254, Train Acc=98.75%, Val Acc=99.17%
2025-08-01 11:26:24 - pulsar_trainer - INFO - Validation F1: 99.1667, Precision: 99.1682, Recall: 99.1667
2025-08-01 11:26:24 - pulsar_trainer - INFO - 早停触发，在第 23 轮停止训练
2025-08-01 11:26:24 - pulsar_trainer - INFO - ============================================================
2025-08-01 11:26:24 - pulsar_trainer - INFO - ✅ 训练完成
2025-08-01 11:26:24 - pulsar_trainer - INFO - 🏆 最佳验证准确率: 99.1667
2025-08-01 11:26:24 - pulsar_trainer - INFO - ⏱️ 总训练时间: 151.39秒
2025-08-01 11:26:24 - pulsar_trainer - INFO - ============================================================
2025-08-01 11:26:24 - pulsar_trainer - INFO - 🔍 开始最终评估...
2025-08-01 11:26:24 - pulsar_trainer - INFO - 模型已加载: D:\pulsarSuanfa\outputs_MULTIMODAL\models\best_model.pth
2025-08-01 11:26:47 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 开始全面误分类分析...
2025-08-01 11:26:47 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 综合误分类分析报告已生成: D:\pulsarSuanfa\outputs_MULTIMODAL\results\comprehensive_misclassification_analysis\comprehensive_misclassification_report.txt
2025-08-01 11:26:47 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 全面误分类分析完成。报告保存至: D:\pulsarSuanfa\outputs_MULTIMODAL\results\comprehensive_misclassification_analysis
2025-08-01 11:26:47 - pulsar_trainer - INFO - 📊 评估结果:
2025-08-01 11:26:47 - pulsar_trainer - INFO -   - accuracy: 0.9860
2025-08-01 11:26:47 - pulsar_trainer - INFO -   - precision: 0.9833
2025-08-01 11:26:47 - pulsar_trainer - INFO -   - recall: 0.9888
2025-08-01 11:26:47 - pulsar_trainer - INFO -   - specificity: 0.9832
2025-08-01 11:26:47 - pulsar_trainer - INFO -   - f1_score: 0.9861
2025-08-01 11:26:47 - pulsar_trainer - INFO -   - false_positive_rate: 0.0168
2025-08-01 11:26:50 - pulsar_trainer - INFO - 所有结果已保存到: D:\pulsarSuanfa\outputs_MULTIMODAL\results
2025-08-01 11:26:50 - pulsar_trainer - INFO - 可视化图表已保存到: D:\pulsarSuanfa\outputs_MULTIMODAL\plots
