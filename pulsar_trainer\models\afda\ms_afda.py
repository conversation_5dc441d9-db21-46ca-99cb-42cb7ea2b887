"""
多尺度自适应频域注意力模块 (MS-AFDA)
Multi-Scale Adaptive Frequency Domain Attention
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Tuple
from .afda_base import AFDAModule


class MultiScaleAFDA(AFDAModule):
    """多尺度自适应频域注意力实现"""
    
    def __init__(self, 
                 scales: List[int] = [16, 32, 64],
                 fusion_strategy: str = "adaptive_weighted",
                 **kwargs):
        super().__init__(**kwargs)
        
        self.scales = scales
        self.fusion_strategy = fusion_strategy
        self.num_scales = len(scales)
        
        # 为每个尺度创建独立的注意力头
        self.scale_attention_heads = nn.ModuleList([
            nn.Sequential(
                nn.Linear(self.feature_dim, self.attention_heads),
                nn.GELU(),
                nn.Linear(self.attention_heads, self.attention_heads)
            ) for _ in scales
        ])
        
        # 多尺度特征融合权重
        if fusion_strategy == "adaptive_weighted":
            self.fusion_weights = nn.Parameter(torch.ones(self.num_scales))
            self.fusion_mlp = nn.Sequential(
                nn.Linear(self.feature_dim * self.num_scales, self.feature_dim),
                nn.GELU(),
                nn.Linear(self.feature_dim, self.num_scales),
                nn.Softmax(dim=-1)
            )
        
    def _multi_scale_fft(self, x: torch.Tensor) -> List[torch.Tensor]:
        """多尺度FFT变换"""
        B, C, H, W = x.shape
        x_flat = x.view(B, C, H * W).permute(0, 2, 1)  # (B, H*W, C)
        
        multi_scale_features = []
        
        for i, scale in enumerate(self.scales):
            # 调整序列长度到指定尺度
            if H * W > scale:
                # 下采样到指定尺度
                indices = torch.linspace(0, H*W-1, scale, dtype=torch.long, device=x.device)
                x_scale = x_flat[:, indices, :]  # (B, scale, C)
            else:
                # 如果原始长度小于尺度，进行零填充
                pad_size = scale - H * W
                x_scale = F.pad(x_flat, (0, 0, 0, pad_size))  # (B, scale, C)
            
            # 重新排列为多头格式
            x_heads = x_scale.view(B, scale, self.attention_heads, self.head_dim).permute(0, 2, 1, 3)
            
            # FFT变换
            F_fft = torch.fft.rfft(x_heads, dim=2, norm='ortho')
            multi_scale_features.append(F_fft)
            
        return multi_scale_features
        
    def _adaptive_fusion(self, multi_scale_features: List[torch.Tensor]) -> torch.Tensor:
        """自适应特征融合"""
        B = multi_scale_features[0].shape[0]
        
        if self.fusion_strategy == "adaptive_weighted":
            # 计算每个尺度特征的全局表示
            scale_representations = []
            for feat in multi_scale_features:
                # feat: (B, attention_heads, freq_bins, head_dim)
                global_repr = feat.mean(dim=(1, 2))  # (B, head_dim)
                scale_representations.append(global_repr)
            
            # 拼接所有尺度的表示
            concat_repr = torch.cat(scale_representations, dim=-1)  # (B, num_scales * head_dim)
            
            # 计算自适应融合权重
            fusion_weights = self.fusion_mlp(concat_repr)  # (B, num_scales)
            
            # 加权融合特征
            # 首先将所有特征调整到相同尺寸（使用最大尺度）
            max_freq_bins = max([feat.shape[2] for feat in multi_scale_features])
            aligned_features = []
            
            for feat in multi_scale_features:
                if feat.shape[2] < max_freq_bins:
                    # 零填充到最大尺寸
                    pad_size = max_freq_bins - feat.shape[2]
                    feat_padded = F.pad(feat, (0, 0, 0, pad_size))
                    aligned_features.append(feat_padded)
                else:
                    aligned_features.append(feat)
            
            # 加权融合
            fused_feature = torch.zeros_like(aligned_features[0])
            for i, feat in enumerate(aligned_features):
                weight = fusion_weights[:, i:i+1, None, None]  # (B, 1, 1, 1)
                fused_feature += weight * feat
                
            return fused_feature
        
        else:
            # 简单平均融合
            return torch.stack(multi_scale_features).mean(dim=0)
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """多尺度AFDA前向传播"""
        original_shape = x.shape
        B, C, H, W = x.shape
        
        # 1. 预归一化
        x_flat = x.view(B, C, H * W).permute(0, 2, 1)  # (B, H*W, C)
        x_norm = self.pre_norm(x_flat)
        
        # 2. 多尺度FFT变换
        multi_scale_fft = self._multi_scale_fft(x)
        
        # 3. 对每个尺度应用注意力机制
        enhanced_features = []
        for i, F_fft in enumerate(multi_scale_fft):
            # 计算该尺度的自适应参数
            scale_repr = F_fft.mean(dim=(1, 2))  # (B, head_dim)
            adaptive_params = self.scale_attention_heads[i](scale_repr)  # (B, attention_heads)
            
            # 扩展维度以匹配频域特征
            freq_bins = F_fft.shape[2]
            adaptive_scale = adaptive_params.unsqueeze(-1).unsqueeze(-1)  # (B, attention_heads, 1, 1)
            adaptive_scale = adaptive_scale.expand(-1, -1, freq_bins, 1)
            
            # 应用自适应调制
            base_filter = self.base_filter[:, :freq_bins, :]  # 调整到当前频率桶数
            base_bias = self.base_bias[:, :freq_bins, :]
            
            effective_filter = base_filter * (1 + adaptive_scale)
            effective_bias = base_bias
            
            # 频域增强
            F_fft_mod = F_fft * effective_filter + effective_bias
            F_fft_nl = self.complex_activation(F_fft_mod)
            
            enhanced_features.append(F_fft_nl)
        
        # 4. 自适应特征融合
        fused_feature = self._adaptive_fusion(enhanced_features)
        
        # 5. IFFT逆变换回到空间域
        x_filtered = torch.fft.irfft(fused_feature, dim=2, n=H*W, norm='ortho')
        x_filtered = x_filtered.permute(0, 2, 1, 3).reshape(B, H*W, C)
        
        # 6. 残差连接
        output = x_norm + self.dropout(x_filtered)
        output = output.permute(0, 2, 1).view(B, C, H, W)
        
        return output
