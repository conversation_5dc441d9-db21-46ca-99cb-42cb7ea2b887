#!/usr/bin/env python3
"""
测试修复后的PathManager路径解析功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pathlib import Path
from pulsar_trainer.utils.path_manager import PathManager

def test_path_manager_fix():
    """测试PathManager修复后的路径解析"""
    print("="*60)
    print("测试PathManager路径解析修复")
    print("="*60)
    
    # 创建PathManager实例
    path_manager = PathManager()
    print(f"项目根目录: {path_manager.project_root}")
    
    # 测试不同的配置文件路径格式
    test_cases = [
        # (输入路径, 期望的结果描述)
        ("coatnet_config.yaml", "基础配置文件名"),
        ("config/coatnet_config.yaml", "包含config/前缀的相对路径"),
        ("config/coatnet_config_1_5.yaml", "1:5配置文件路径"),
        ("config/coatnet_config_1_8.yaml", "1:8配置文件路径"),
        ("pulsar_trainer/config/coatnet_config.yaml", "完整相对路径"),
        (str(Path("D:/pulsarSuanfa/pulsar_trainer/config/coatnet_config.yaml")), "绝对路径")
    ]
    
    print(f"\n测试路径解析:")
    print("-" * 60)
    
    all_passed = True
    
    for input_path, description in test_cases:
        try:
            resolved_path = path_manager.get_config_path(input_path)
            print(f"\n{description}:")
            print(f"  输入: {input_path}")
            print(f"  解析: {resolved_path}")
            
            # 检查路径是否合理（不应该有重复的config目录）
            path_str = str(resolved_path)
            if "config\\config" in path_str or "config/config" in path_str:
                print(f"  ❌ 路径包含重复的config目录")
                all_passed = False
            else:
                print(f"  ✓ 路径解析正确")
                
            # 检查文件是否存在（对于实际存在的配置文件）
            if resolved_path.exists():
                print(f"  ✓ 文件存在")
            else:
                print(f"  ⚠️ 文件不存在（可能是测试路径）")
                
        except Exception as e:
            print(f"  ❌ 路径解析失败: {e}")
            all_passed = False
    
    # 特别测试问题路径
    print(f"\n特别测试问题路径:")
    print("-" * 30)
    
    problem_paths = [
        "config/coatnet_config_1_5.yaml",
        "config/coatnet_config_1_8.yaml"
    ]
    
    for problem_path in problem_paths:
        try:
            resolved_path = path_manager.get_config_path(problem_path)
            print(f"\n问题路径: {problem_path}")
            print(f"解析结果: {resolved_path}")
            
            # 检查是否修复了重复config目录的问题
            if "config\\config" in str(resolved_path) or "config/config" in str(resolved_path):
                print(f"❌ 仍然存在重复config目录问题")
                all_passed = False
            else:
                print(f"✓ 重复config目录问题已修复")
                
            # 检查文件是否存在
            if resolved_path.exists():
                print(f"✓ 配置文件存在，可以正常使用")
            else:
                print(f"❌ 配置文件不存在: {resolved_path}")
                all_passed = False
                
        except Exception as e:
            print(f"❌ 路径解析异常: {e}")
            all_passed = False
    
    print(f"\n{'='*60}")
    if all_passed:
        print("✅ PathManager路径解析修复成功！")
        print("✅ 所有路径都能正确解析")
        print("✅ 重复config目录问题已解决")
        return True
    else:
        print("❌ PathManager路径解析仍有问题")
        return False

def main():
    """主测试函数"""
    print("开始测试PathManager修复...")
    
    success = test_path_manager_fix()
    
    if success:
        print("\n🚀 PathManager修复验证成功！")
        print("现在可以正常启动训练了。")
        return True
    else:
        print("\n❌ PathManager修复验证失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
