"""
AFDA基类模块 - 自适应频域注意力机制基础实现
基于FFTTransformerEncoderBlock设计
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Tuple


class AFDAModule(nn.Module):
    """AFDA基类，定义通用接口和基础功能"""
    
    def __init__(self, 
                 input_channels: int = 3,
                 feature_dim: int = 512,
                 frequency_bins: int = 33,  # 64//2 + 1
                 attention_heads: int = 8,
                 dropout: float = 0.1):
        super().__init__()
        
        # 基础参数设置
        self.input_channels = input_channels
        self.feature_dim = feature_dim
        self.frequency_bins = frequency_bins
        self.attention_heads = attention_heads
        self.head_dim = feature_dim // attention_heads
        
        if feature_dim % attention_heads != 0:
            raise ValueError("feature_dim必须能被attention_heads整除")
        
        # FFT相关参数
        self.seq_len = 64  # 输入序列长度
        
        # 注意力机制参数
        self.base_filter = nn.Parameter(torch.ones(attention_heads, self.frequency_bins, 1))
        self.base_bias = nn.Parameter(torch.full((attention_heads, self.frequency_bins, 1), -0.1))
        
        # 自适应MLP
        self.adaptive_mlp = nn.Sequential(
            nn.Linear(feature_dim, feature_dim),
            nn.GELU(),
            nn.Linear(feature_dim, attention_heads * self.frequency_bins * 2)
        )
        
        self.dropout = nn.Dropout(dropout)
        self.pre_norm = nn.LayerNorm(feature_dim)
        
    def complex_activation(self, z: torch.Tensor) -> torch.Tensor:
        """
        对复数张量应用非线性激活函数
        参数:
          z: 形状为 (B, num_heads, freq_bins, head_dim) 的复数张量
        返回:
          经过非线性变换的复数张量,形状相同
        """
        mag = torch.abs(z)
        # 对幅度进行非线性变换,GELU 提供平滑的非线性
        mag_act = F.gelu(mag)
        # 计算缩放因子,防止除零错误
        scale = mag_act / (mag + 1e-6)
        return z * scale
        
    def _fft_transform(self, x: torch.Tensor) -> torch.Tensor:
        """执行FFT变换"""
        # x: (B, C, H, W) -> (B, H*W, C)
        B, C, H, W = x.shape
        x = x.view(B, C, H * W).permute(0, 2, 1)  # (B, H*W, C)
        
        # 沿着序列维度计算FFT
        F_fft = torch.fft.rfft(x, dim=1, norm='ortho')
        return F_fft
        
    def _attention_weights(self, freq_features: torch.Tensor) -> torch.Tensor:
        """计算频域注意力权重"""
        B, freq_bins, D = freq_features.shape
        
        # 重新排列张量以分离不同的注意力头
        x_heads = freq_features.view(B, freq_bins, self.attention_heads, self.head_dim).permute(0, 2, 1, 3)
        
        # 计算自适应调制参数
        adaptive_params = self.adaptive_mlp(freq_features.mean(dim=1))  # (B, attention_heads * freq_bins * 2)
        adaptive_params = adaptive_params.view(B, self.attention_heads, self.frequency_bins, 2)
        adaptive_scale = adaptive_params[..., 0:1]  # (B, attention_heads, freq_bins, 1)
        adaptive_bias = adaptive_params[..., 1:2]   # (B, attention_heads, freq_bins, 1)
        
        # 结合基础滤波器和自适应调制参数
        effective_filter = self.base_filter * (1 + adaptive_scale)
        effective_bias = self.base_bias + adaptive_bias
        
        return effective_filter, effective_bias
        
    def _ifft_transform(self, freq_features: torch.Tensor, original_shape: Tuple[int, int, int, int]) -> torch.Tensor:
        """执行IFFT逆变换"""
        B, C, H, W = original_shape
        
        # 逆傅里叶变换还原到时序空间
        x_filtered = torch.fft.irfft(freq_features, dim=1, n=H*W, norm='ortho')
        
        # 重新排列张量回到原始形状
        x_filtered = x_filtered.permute(0, 2, 1).view(B, C, H, W)
        
        return x_filtered
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """前向传播主流程"""
        original_shape = x.shape
        B, C, H, W = x.shape
        
        # 1. 预归一化
        x_flat = x.view(B, C, H * W).permute(0, 2, 1)  # (B, H*W, C)
        x_norm = self.pre_norm(x_flat)
        
        # 2. FFT变换
        x_heads = x_norm.view(B, H*W, self.attention_heads, self.head_dim).permute(0, 2, 1, 3)
        F_fft = torch.fft.rfft(x_heads, dim=2, norm='ortho')
        
        # 3. 注意力计算
        adaptive_params = self.adaptive_mlp(x_norm.mean(dim=1))
        adaptive_params = adaptive_params.view(B, self.attention_heads, self.frequency_bins, 2)
        adaptive_scale = adaptive_params[..., 0:1]
        adaptive_bias = adaptive_params[..., 1:2]
        
        effective_filter = self.base_filter * (1 + adaptive_scale)
        effective_bias = self.base_bias + adaptive_bias
        
        # 4. 特征增强
        F_fft_mod = F_fft * effective_filter + effective_bias
        F_fft_nl = self.complex_activation(F_fft_mod)
        
        # 5. IFFT逆变换
        x_filtered = torch.fft.irfft(F_fft_nl, dim=2, n=H*W, norm='ortho')
        x_filtered = x_filtered.permute(0, 2, 1, 3).reshape(B, H*W, C)
        
        # 6. 残差连接
        output = x_norm + self.dropout(x_filtered)
        output = output.permute(0, 2, 1).view(B, C, H, W)
        
        return output
