#!/usr/bin/env python3
"""
AFDA配置验证脚本
验证三个配置文件的AFDA配置：配置加载、参数验证、差异化配置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import yaml
from pathlib import Path

from pulsar_trainer.utils.config import load_config_with_classes, AFDAConfig
from pulsar_trainer.models.coatnet import create_coatnet_from_config


def test_config_loading():
    """测试配置文件加载"""
    print("\n" + "="*60)
    print("测试配置文件加载")
    print("="*60)
    
    config_files = [
        ("pulsar_trainer/config/coatnet_config.yaml", "1:1平衡数据集"),
        ("pulsar_trainer/config/coatnet_config_1_5.yaml", "1:5不平衡数据集"),
        ("pulsar_trainer/config/coatnet_config_1_8.yaml", "1:8不平衡数据集")
    ]
    
    loaded_configs = {}
    
    for config_path, dataset_name in config_files:
        print(f"\n📋 加载配置: {dataset_name}")
        print(f"  文件路径: {config_path}")
        
        try:
            # 检查文件是否存在
            if not Path(config_path).exists():
                print(f"❌ 配置文件不存在: {config_path}")
                return False, {}
            
            # 加载配置
            config = load_config_with_classes(config_path)
            loaded_configs[dataset_name] = config
            
            print("✓ 配置文件加载成功")
            
            # 检查AFDA配置是否存在
            if 'afda' in config['model']:
                afda_config = config['model']['afda']
                print(f"  AFDA启用状态: {afda_config.enabled}")
                print(f"  AFDA架构: {afda_config.architecture}")
                print("✓ AFDA配置存在")
            else:
                print("❌ AFDA配置不存在")
                return False, {}
                
        except Exception as e:
            print(f"❌ 配置加载失败: {e}")
            import traceback
            traceback.print_exc()
            return False, {}
    
    print("\n✅ 所有配置文件加载成功")
    return True, loaded_configs


def test_afda_config_validation():
    """测试AFDA配置验证"""
    print("\n" + "="*60)
    print("测试AFDA配置验证")
    print("="*60)
    
    success, configs = test_config_loading()
    if not success:
        return False
    
    for dataset_name, config in configs.items():
        print(f"\n🔍 验证配置: {dataset_name}")
        
        try:
            afda_config = config['model']['afda']
            
            # 验证基础配置
            assert afda_config.enabled == True, "AFDA应该启用"
            assert afda_config.architecture == "ms_pc_hybrid", "架构应该是ms_pc_hybrid"
            
            # 验证多尺度配置
            ms_config = afda_config.multi_scale
            assert "scales" in ms_config, "多尺度配置应包含scales"
            assert "attention_heads" in ms_config, "多尺度配置应包含attention_heads"
            assert "fusion_strategy" in ms_config, "多尺度配置应包含fusion_strategy"
            
            print(f"  多尺度配置: {ms_config['scales']}")
            print(f"  注意力头数: {ms_config['attention_heads']}")
            print(f"  融合策略: {ms_config['fusion_strategy']}")
            
            # 验证物理约束配置
            pc_config = afda_config.physical_constraints
            assert "dispersion" in pc_config, "物理约束应包含dispersion"
            assert "periodicity" in pc_config, "物理约束应包含periodicity"
            
            disp_weight = pc_config["dispersion"]["weight"]
            period_weight = pc_config["periodicity"]["weight"]
            print(f"  色散权重: {disp_weight}")
            print(f"  周期性权重: {period_weight}")
            
            # 验证自适应融合配置
            af_config = afda_config.adaptive_fusion
            assert "threshold" in af_config, "自适应融合应包含threshold"
            assert "temperature" in af_config, "自适应融合应包含temperature"
            assert "dropout" in af_config, "自适应融合应包含dropout"
            
            print(f"  自适应阈值: {af_config['threshold']}")
            print(f"  温度参数: {af_config['temperature']}")
            print(f"  Dropout: {af_config['dropout']}")
            
            print(f"✓ {dataset_name} 配置验证通过")
            
        except Exception as e:
            print(f"❌ {dataset_name} 配置验证失败: {e}")
            return False
    
    print("\n✅ 所有AFDA配置验证通过")
    return True


def test_config_differences():
    """测试配置差异化"""
    print("\n" + "="*60)
    print("测试配置差异化")
    print("="*60)
    
    success, configs = test_config_loading()
    if not success:
        return False
    
    # 提取AFDA配置
    afda_configs = {}
    for dataset_name, config in configs.items():
        afda_configs[dataset_name] = config['model']['afda']
    
    # 检查差异化参数
    print("\n📊 配置差异化分析:")
    print("-" * 40)
    
    # 自适应融合阈值差异
    thresholds = {}
    for dataset_name, afda_config in afda_configs.items():
        threshold = afda_config.adaptive_fusion['threshold']
        thresholds[dataset_name] = threshold
        print(f"{dataset_name:15} 阈值: {threshold}")
    
    # 验证阈值递增趋势
    expected_order = ["1:1平衡数据集", "1:5不平衡数据集", "1:8不平衡数据集"]
    for i in range(len(expected_order) - 1):
        current = thresholds[expected_order[i]]
        next_val = thresholds[expected_order[i + 1]]
        if next_val <= current:
            print(f"❌ 阈值应该递增: {expected_order[i]} ({current}) -> {expected_order[i+1]} ({next_val})")
            return False
    
    print("✓ 阈值递增趋势正确")
    
    # 物理约束权重差异
    print("\n物理约束权重:")
    for dataset_name, afda_config in afda_configs.items():
        disp_weight = afda_config.physical_constraints["dispersion"]["weight"]
        period_weight = afda_config.physical_constraints["periodicity"]["weight"]
        print(f"{dataset_name:15} 色散: {disp_weight}, 周期性: {period_weight}")
    
    # 温度参数差异
    print("\n温度参数:")
    for dataset_name, afda_config in afda_configs.items():
        temperature = afda_config.adaptive_fusion['temperature']
        print(f"{dataset_name:15} 温度: {temperature}")
    
    print("\n✅ 配置差异化验证通过")
    return True


def test_model_creation_with_configs():
    """测试使用配置创建模型"""
    print("\n" + "="*60)
    print("测试使用配置创建模型")
    print("="*60)
    
    success, configs = test_config_loading()
    if not success:
        return False
    
    for dataset_name, config in configs.items():
        print(f"\n🏗️ 创建模型: {dataset_name}")
        
        try:
            model_config = config['model']['coatnet']
            model = create_coatnet_from_config(model_config)
            
            # 检查AFDA模块是否正确集成
            if hasattr(model, 'afda_module') and model.afda_module is not None:
                print("✓ AFDA模块正确集成")
                
                # 检查参数数量
                afda_params = sum(p.numel() for p in model.afda_module.parameters())
                total_params = sum(p.numel() for p in model.parameters())
                afda_ratio = afda_params / total_params * 100
                
                print(f"  AFDA参数: {afda_params:,}")
                print(f"  总参数: {total_params:,}")
                print(f"  AFDA占比: {afda_ratio:.2f}%")
                
                # 测试前向传播
                test_input = torch.randn(2, 3, 64, 64)
                with torch.no_grad():
                    output = model(test_input)
                
                print(f"  输出形状: {output.shape}")
                print(f"✓ {dataset_name} 模型创建和测试成功")
                
            else:
                print("❌ AFDA模块未正确集成")
                return False
                
        except Exception as e:
            print(f"❌ {dataset_name} 模型创建失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    print("\n✅ 所有配置的模型创建成功")
    return True


def test_yaml_syntax():
    """测试YAML语法正确性"""
    print("\n" + "="*60)
    print("测试YAML语法正确性")
    print("="*60)
    
    config_files = [
        "pulsar_trainer/config/coatnet_config.yaml",
        "pulsar_trainer/config/coatnet_config_1_5.yaml",
        "pulsar_trainer/config/coatnet_config_1_8.yaml"
    ]
    
    for config_path in config_files:
        print(f"\n📝 检查YAML语法: {config_path}")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                yaml.safe_load(f)
            print("✓ YAML语法正确")
            
        except yaml.YAMLError as e:
            print(f"❌ YAML语法错误: {e}")
            return False
        except Exception as e:
            print(f"❌ 文件读取错误: {e}")
            return False
    
    print("\n✅ 所有YAML文件语法正确")
    return True


def main():
    """主测试函数"""
    print("开始AFDA配置验证测试...")
    
    tests = [
        ("YAML语法检查", test_yaml_syntax),
        ("配置文件加载", lambda: test_config_loading()[0]),
        ("AFDA配置验证", test_afda_config_validation),
        ("配置差异化", test_config_differences),
        ("模型创建测试", test_model_creation_with_configs)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 执行测试: {test_name}")
        if test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print(f"\n{'='*60}")
    print("AFDA配置验证测试结果汇总")
    print(f"{'='*60}")
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有AFDA配置验证测试通过！")
        print("✅ 配置加载、参数验证、差异化配置全部正常")
        return True
    else:
        print("❌ 部分AFDA配置验证测试失败")
        return False


if __name__ == "__main__":
    import torch
    success = main()
    sys.exit(0 if success else 1)
