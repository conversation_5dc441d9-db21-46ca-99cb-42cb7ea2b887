#!/usr/bin/env python3
"""
3通道多模态脉冲星分类系统性能对比分析
对比1:1、1:5、1:8三种比例数据集的性能表现
"""

import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path

def create_performance_comparison():
    """创建性能对比分析"""
    print("="*80)
    print("3通道多模态脉冲星分类系统 - 性能对比分析")
    print("="*80)
    
    # 性能数据
    datasets = {
        "1:1 平衡": {
            "accuracy": 98.60,
            "precision": 98.33,
            "recall": 98.88,
            "f1_score": 98.61,
            "specificity": 98.32,
            "false_positive_rate": 1.68,
            "training_epochs": 23,
            "training_time": 151.39,
            "dataset_size": 2392,
            "loss_type": "CrossEntropy"
        },
        "1:5 不平衡": {
            "accuracy": 98.89,
            "precision": 95.16,
            "recall": 98.33,
            "f1_score": 96.72,
            "specificity": 99.00,
            "false_positive_rate": 1.00,
            "training_epochs": 37,
            "training_time": 527.57,
            "dataset_size": 7176,
            "loss_type": "Focal (α=2.0, γ=2.0)"
        },
        "1:8 不平衡": {
            "accuracy": 99.07,
            "precision": 93.19,
            "recall": 98.89,
            "f1_score": 95.96,
            "specificity": 99.10,
            "false_positive_rate": 0.90,
            "training_epochs": 26,
            "training_time": 699.67,
            "dataset_size": 10764,
            "loss_type": "Focal (α=3.0, γ=2.5)"
        }
    }
    
    print("\n📊 详细性能对比:")
    print("-" * 80)
    print(f"{'指标':<20} {'1:1平衡':<15} {'1:5不平衡':<15} {'1:8不平衡':<15}")
    print("-" * 80)
    
    metrics = ["accuracy", "precision", "recall", "f1_score", "specificity", "false_positive_rate"]
    metric_names = ["准确率 (%)", "精确率 (%)", "召回率 (%)", "F1分数 (%)", "特异性 (%)", "假阳性率 (%)"]
    
    for metric, name in zip(metrics, metric_names):
        values = [datasets[ds][metric] for ds in datasets.keys()]
        print(f"{name:<20} {values[0]:<15.2f} {values[1]:<15.2f} {values[2]:<15.2f}")
    
    print("-" * 80)
    print(f"{'训练轮数':<20} {datasets['1:1 平衡']['training_epochs']:<15} {datasets['1:5 不平衡']['training_epochs']:<15} {datasets['1:8 不平衡']['training_epochs']:<15}")
    print(f"{'训练时间 (秒)':<20} {datasets['1:1 平衡']['training_time']:<15.1f} {datasets['1:5 不平衡']['training_time']:<15.1f} {datasets['1:8 不平衡']['training_time']:<15.1f}")
    print(f"{'数据集大小':<20} {datasets['1:1 平衡']['dataset_size']:<15} {datasets['1:5 不平衡']['dataset_size']:<15} {datasets['1:8 不平衡']['dataset_size']:<15}")
    
    print("\n🔍 关键发现:")
    print("-" * 40)
    
    # 准确率分析
    accuracies = [datasets[ds]["accuracy"] for ds in datasets.keys()]
    best_acc_idx = np.argmax(accuracies)
    dataset_names = list(datasets.keys())
    print(f"✓ 最高准确率: {dataset_names[best_acc_idx]} ({accuracies[best_acc_idx]:.2f}%)")
    
    # F1分数分析
    f1_scores = [datasets[ds]["f1_score"] for ds in datasets.keys()]
    best_f1_idx = np.argmax(f1_scores)
    print(f"✓ 最高F1分数: {dataset_names[best_f1_idx]} ({f1_scores[best_f1_idx]:.2f}%)")
    
    # 召回率分析
    recalls = [datasets[ds]["recall"] for ds in datasets.keys()]
    best_recall_idx = np.argmax(recalls)
    print(f"✓ 最高召回率: {dataset_names[best_recall_idx]} ({recalls[best_recall_idx]:.2f}%)")
    
    # 假阳性率分析
    fprs = [datasets[ds]["false_positive_rate"] for ds in datasets.keys()]
    best_fpr_idx = np.argmin(fprs)
    print(f"✓ 最低假阳性率: {dataset_names[best_fpr_idx]} ({fprs[best_fpr_idx]:.2f}%)")
    
    print("\n📈 趋势分析:")
    print("-" * 40)
    print("随着数据不平衡程度增加:")
    print(f"• 准确率: {accuracies[0]:.2f}% → {accuracies[1]:.2f}% → {accuracies[2]:.2f}% (↗️ 持续提升)")
    print(f"• F1分数: {f1_scores[0]:.2f}% → {f1_scores[1]:.2f}% → {f1_scores[2]:.2f}% (↘️ 轻微下降)")
    print(f"• 召回率: {recalls[0]:.2f}% → {recalls[1]:.2f}% → {recalls[2]:.2f}% (↗️ 基本稳定)")
    print(f"• 假阳性率: {fprs[0]:.2f}% → {fprs[1]:.2f}% → {fprs[2]:.2f}% (↘️ 显著改善)")
    
    print("\n🎯 焦点损失效果分析:")
    print("-" * 40)
    print("• 1:5数据集 (α=2.0, γ=2.0):")
    print(f"  - 相比1:1平衡数据集，假阳性率从{fprs[0]:.2f}%降至{fprs[1]:.2f}%")
    print(f"  - F1分数轻微下降{f1_scores[0] - f1_scores[1]:.2f}%，但仍保持96.72%高水平")
    
    print("• 1:8数据集 (α=3.0, γ=2.5):")
    print(f"  - 假阳性率进一步降至{fprs[2]:.2f}%，达到最佳水平")
    print(f"  - 准确率达到最高{accuracies[2]:.2f}%")
    print(f"  - 召回率保持{recalls[2]:.2f}%，脉冲星检测能力优秀")
    
    print("\n🏆 综合评价:")
    print("-" * 40)
    print("✅ 3通道多模态系统在所有比例数据集上都表现优异")
    print("✅ 焦点损失有效处理不平衡数据，显著降低假阳性率")
    print("✅ 系统在高度不平衡数据(1:8)上仍保持99%+准确率")
    print("✅ 召回率在所有数据集上都保持98%+，脉冲星检测能力强")
    print("✅ T+F+T×F多模态融合架构证明有效")
    
    print("\n📊 推荐使用场景:")
    print("-" * 40)
    print("• 1:1平衡数据集: 综合性能最佳，适合一般分类任务")
    print("• 1:5不平衡数据集: 平衡性能与效率，适合中等不平衡场景")
    print("• 1:8不平衡数据集: 最低误报率，适合对假阳性敏感的应用")
    
    return datasets

def main():
    """主函数"""
    print("开始3通道多模态脉冲星分类系统性能对比分析...")
    
    datasets = create_performance_comparison()
    
    print(f"\n{'='*80}")
    print("🎉 性能对比分析完成！")
    print("✅ 3通道多模态脉冲星分类系统在所有测试场景下都表现优异")
    print("✅ 焦点损失成功解决不平衡数据问题")
    print("✅ 系统已完全就绪，可投入实际应用")
    print(f"{'='*80}")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
