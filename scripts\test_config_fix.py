#!/usr/bin/env python3
"""
测试修复后的1:5和1:8配置文件
验证CoAtNet模型能够正确初始化
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
import yaml
from pathlib import Path

from pulsar_trainer.models.coatnet import CoAtNet

def test_config_and_model(config_path, dataset_name):
    """测试配置文件和模型初始化"""
    print(f"\n{'='*60}")
    print(f"测试 {dataset_name} 配置文件")
    print(f"{'='*60}")
    
    try:
        # 1. 加载配置文件
        print(f"加载配置文件: {config_path}")
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print("✓ 配置文件加载成功")
        
        # 2. 检查模型配置
        model_config = config['model']['coatnet']
        print(f"  数据根目录: {config['data']['data_root']}")
        print(f"  模态: {config['data']['modality']}")
        print(f"  输入通道: {model_config['in_channels']}")
        print(f"  图像尺寸: {model_config['image_size']}")
        print(f"  通道数: {model_config['channels']}")
        print(f"  块数量: {model_config['num_blocks']}")
        print(f"  块类型: {model_config['block_types']}")
        
        # 3. 检查损失函数配置
        loss_config = config['training']['loss']
        print(f"  损失类型: {loss_config['type']}")
        print(f"  Alpha: {loss_config['alpha']}")
        print(f"  Gamma: {loss_config['gamma']}")
        
        # 4. 创建模型
        print("\n创建CoAtNet模型...")
        model = CoAtNet(
            image_size=tuple(model_config['image_size']),
            in_channels=model_config['in_channels'],
            num_blocks=model_config['num_blocks'],
            channels=model_config['channels'],
            num_classes=model_config['num_classes'],
            block_types=model_config['block_types']
        )
        
        print("✓ 模型创建成功")
        total_params = sum(p.numel() for p in model.parameters())
        print(f"  模型参数数: {total_params:,}")
        
        # 5. 测试前向传播
        print("\n测试前向传播...")
        model.eval()
        test_input = torch.randn(4, 3, 64, 64)
        
        with torch.no_grad():
            output = model(test_input)
        
        print(f"✓ 前向传播成功")
        print(f"  输入形状: {test_input.shape}")
        print(f"  输出形状: {output.shape}")
        
        # 6. 验证数据集路径
        data_root = Path(config['data']['data_root'])
        if data_root.exists():
            print(f"✓ 数据集路径存在: {data_root}")
            
            # 统计文件数量
            train_files = len(list((data_root / 'MULTIMODAL' / 'train').glob('*.npy')))
            val_files = len(list((data_root / 'MULTIMODAL' / 'validation').glob('*.npy')))
            test_files = len(list((data_root / 'MULTIMODAL' / 'test').glob('*.npy')))
            
            print(f"  训练文件: {train_files}")
            print(f"  验证文件: {val_files}")
            print(f"  测试文件: {test_files}")
            print(f"  总文件: {train_files + val_files + test_files}")
        else:
            print(f"❌ 数据集路径不存在: {data_root}")
            return False
        
        print(f"\n✅ {dataset_name} 配置测试完全成功！")
        return True
        
    except Exception as e:
        print(f"\n❌ {dataset_name} 配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试修复后的配置文件...")
    
    configs = [
        ("pulsar_trainer/config/coatnet_config_1_5.yaml", "1:5不平衡数据集"),
        ("pulsar_trainer/config/coatnet_config_1_8.yaml", "1:8不平衡数据集")
    ]
    
    passed = 0
    total = len(configs)
    
    for config_path, dataset_name in configs:
        if test_config_and_model(config_path, dataset_name):
            passed += 1
    
    print(f"\n{'='*60}")
    print("测试结果汇总")
    print(f"{'='*60}")
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有配置文件修复成功！")
        print("✅ 1:5和1:8不平衡数据集配置完全正常")
        print("\n建议的训练命令:")
        print("1. 1:5不平衡数据集:")
        print("   python pulsar_trainer/train.py --config config/coatnet_config_1_5.yaml")
        print("2. 1:8不平衡数据集:")
        print("   python pulsar_trainer/train.py --config config/coatnet_config_1_8.yaml")
        return True
    else:
        print("❌ 部分配置文件仍有问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
