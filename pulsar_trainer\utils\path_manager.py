"""
跨环境路径管理工具

该模块提供统一的路径管理功能，确保项目在不同环境和操作系统中都能正确运行。
支持自动项目根目录检测、相对路径解析、配置文件路径管理等功能。
"""

import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional, Union


class PathManager:
    """
    统一的路径管理器，确保跨环境兼容性。

    该类提供以下功能：
    - 自动检测项目根目录
    - 统一的配置文件路径管理
    - 相对路径和绝对路径的智能处理
    - 跨平台路径兼容性
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化路径管理器。

        Args:
            config: 可选的配置字典
        """
        self.config = config or {}
        self.project_root = self._detect_project_root()

    def _detect_project_root(self) -> Path:
        """
        自动检测项目根目录。

        使用多种策略来检测项目根目录：
        1. 从当前文件向上查找包含特定标识的目录
        2. 从当前工作目录查找
        3. 从sys.path查找
        4. 默认返回当前工作目录

        Returns:
            项目根目录的Path对象
        """
        # 方法1：从当前文件向上查找
        current_file = Path(__file__).resolve()
        for parent in current_file.parents:
            if (parent / "pulsar_trainer").exists() and (parent / "config").exists():
                return parent

        # 方法2：从工作目录查找
        cwd = Path.cwd()
        if (cwd / "pulsar_trainer").exists():
            return cwd

        # 方法3：从sys.path查找
        for path_str in sys.path:
            if path_str:  # 避免空字符串
                path = Path(path_str)
                if (path / "pulsar_trainer").exists():
                    return path

        # 方法4：检查父目录
        for parent in cwd.parents:
            if (parent / "pulsar_trainer").exists():
                return parent

        # 默认返回当前工作目录
        return cwd

    def get_config_path(self, config_name: str = "coatnet_config.yaml") -> Path:
        """
        获取配置文件路径。

        Args:
            config_name: 配置文件名或相对路径

        Returns:
            配置文件的绝对路径
        """
        config_path = Path(config_name)

        # 如果是绝对路径，直接返回
        if config_path.is_absolute():
            return config_path

        # 如果相对路径已经包含config目录，直接相对于项目根目录
        if config_name.startswith("config/") or config_name.startswith("config\\"):
            return self.project_root / config_name

        # 否则，相对于项目根目录的config目录
        return self.project_root / "config" / config_name

    def get_output_dir(self, modality: str) -> Path:
        """
        获取输出目录。

        Args:
            modality: 数据模态 (FPP 或 TPP)

        Returns:
            输出目录的绝对路径
        """
        output_config = self.config.get('output', {})
        base_dir = output_config.get('base_dir', output_config.get('output_dir', 'outputs'))

        output_dir = Path(f"{base_dir}_{modality}")

        # 确保是绝对路径
        if not output_dir.is_absolute():
            output_dir = self.project_root / output_dir

        return output_dir

    def ensure_path_exists(self, path: Union[str, Path]) -> Path:
        """
        确保路径存在，如果不存在则创建。

        Args:
            path: 要确保存在的路径

        Returns:
            路径的Path对象
        """
        path_obj = Path(path)
        path_obj.mkdir(parents=True, exist_ok=True)
        return path_obj

    def resolve_data_path(self, data_root: str) -> Path:
        """
        解析数据路径，支持相对路径和绝对路径。

        Args:
            data_root: 数据根目录路径

        Returns:
            解析后的绝对路径
        """
        data_path = Path(data_root)

        if data_path.is_absolute():
            return data_path
        else:
            # 相对路径相对于项目根目录
            return self.project_root / data_path

    def get_relative_to_project(self, path: Union[str, Path]) -> Path:
        """
        获取相对于项目根目录的相对路径。

        Args:
            path: 要转换的路径

        Returns:
            相对于项目根目录的路径
        """
        path_obj = Path(path).resolve()
        try:
            return path_obj.relative_to(self.project_root)
        except ValueError:
            # 如果路径不在项目根目录下，返回绝对路径
            return path_obj

    def normalize_path(self, path: Union[str, Path]) -> Path:
        """
        标准化路径，确保跨平台兼容性。

        Args:
            path: 要标准化的路径

        Returns:
            标准化后的Path对象
        """
        return Path(path).resolve()

    def is_under_project_root(self, path: Union[str, Path]) -> bool:
        """
        检查路径是否在项目根目录下。

        Args:
            path: 要检查的路径

        Returns:
            如果路径在项目根目录下返回True，否则返回False
        """
        try:
            Path(path).resolve().relative_to(self.project_root)
            return True
        except ValueError:
            return False

    def get_safe_filename(self, filename: str) -> str:
        """
        获取安全的文件名，移除或替换不安全的字符。

        Args:
            filename: 原始文件名

        Returns:
            安全的文件名
        """
        # 移除或替换不安全的字符
        unsafe_chars = '<>:"/\\|?*'
        safe_filename = filename
        for char in unsafe_chars:
            safe_filename = safe_filename.replace(char, '_')

        # 移除前后空格和点
        safe_filename = safe_filename.strip(' .')

        # 确保文件名不为空
        if not safe_filename:
            safe_filename = 'unnamed'

        return safe_filename

    @property
    def project_root_str(self) -> str:
        """获取项目根目录的字符串表示。"""
        return str(self.project_root)

    def __str__(self) -> str:
        """字符串表示。"""
        return f"PathManager(project_root={self.project_root})"

    def __repr__(self) -> str:
        """详细字符串表示。"""
        return f"PathManager(project_root={self.project_root!r}, config_keys={list(self.config.keys())})"


def create_path_manager(config: Optional[Dict[str, Any]] = None) -> PathManager:
    """
    创建路径管理器实例的工厂函数。

    Args:
        config: 可选的配置字典

    Returns:
        PathManager实例
    """
    return PathManager(config)


def get_project_root() -> Path:
    """
    获取项目根目录的便捷函数。

    Returns:
        项目根目录的Path对象
    """
    return PathManager()._detect_project_root()
