2025-08-01 11:53:03 - pulsar_trainer - INFO - <PERSON><PERSON> initialized. Log file: D:\pulsarSuanfa\outputs_1_5_MULTIMODAL\logs\pulsar_trainer_20250801_115303.log
2025-08-01 11:53:03 - pulsar_trainer - INFO - 随机种子设置为: 42
2025-08-01 11:53:04 - pulsar_trainer - INFO - 使用配置文件参数创建模型
2025-08-01 11:53:04 - pulsar_trainer - INFO - 模型配置 - num_blocks: [2, 2, 6, 8, 2]
2025-08-01 11:53:04 - pulsar_trainer - INFO - 模型配置 - channels: [96, 128, 256, 512, 1024]
2025-08-01 11:53:04 - pulsar_trainer - INFO - 模型配置 - in_channels: 3
2025-08-01 11:53:04 - pulsar_trainer - INFO - 模型架构详情:
2025-08-01 11:53:04 - pulsar_trainer - INFO -   总参数数量: 38,727,856
2025-08-01 11:53:04 - pulsar_trainer - INFO -   可训练参数: 38,727,856
2025-08-01 11:53:04 - pulsar_trainer - INFO -   模型大小: 147.74 MB
2025-08-01 11:53:04 - pulsar_trainer - INFO -   块类型配置: ['C', 'C', 'T', 'T']
2025-08-01 11:53:04 - pulsar_trainer - INFO -   各阶段块数: [2, 2, 6, 8, 2]
2025-08-01 11:53:04 - pulsar_trainer - INFO -   各阶段通道数: [96, 128, 256, 512, 1024]
2025-08-01 11:53:04 - pulsar_trainer - INFO - Using focal loss with alpha=2.0, gamma=2.0
2025-08-01 11:53:04 - pulsar_trainer - INFO - 🖥️ 使用GPU: NVIDIA GeForce RTX 4050 Laptop GPU
2025-08-01 11:53:04 - pulsar_trainer - INFO - 💾 GPU内存: 6.4GB
2025-08-01 11:53:04 - pulsar_trainer - INFO - 📋 模型参数总数: 38,727,856
2025-08-01 11:53:04 - pulsar_trainer - INFO - 🏗️ 模型架构: CoAtNet
2025-08-01 11:53:04 - pulsar_trainer - INFO - 📚 数据集大小:
2025-08-01 11:53:04 - pulsar_trainer - INFO -   - 训练集: 5022
2025-08-01 11:53:04 - pulsar_trainer - INFO -   - 验证集: 1074
2025-08-01 11:53:04 - pulsar_trainer - INFO -   - 测试集: 1080
2025-08-01 11:53:04 - pulsar_trainer - INFO -   - 总计: 7176
2025-08-01 11:53:04 - pulsar_trainer - INFO - ============================================================
2025-08-01 11:53:04 - pulsar_trainer - INFO - 🚀 开始脉冲星分类训练
2025-08-01 11:53:04 - pulsar_trainer - INFO - 📊 模态: MULTIMODAL
2025-08-01 11:53:04 - pulsar_trainer - INFO - 🧠 模型: coatnet
2025-08-01 11:53:04 - pulsar_trainer - INFO - 📦 批次大小: 32
2025-08-01 11:53:04 - pulsar_trainer - INFO - 🎯 学习率: 0.001
2025-08-01 11:53:04 - pulsar_trainer - INFO - 🔄 训练轮数: 100
2025-08-01 11:53:04 - pulsar_trainer - INFO - ============================================================
2025-08-01 11:54:05 - pulsar_trainer - INFO - Epoch   1: Train Loss=4.0260, Val Loss=0.0740, Train Acc=89.33%, Val Acc=96.83%
2025-08-01 11:54:05 - pulsar_trainer - INFO - Validation F1: 96.8199, Precision: 96.8102, Recall: 96.8343
2025-08-01 11:54:05 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_1_5_MULTIMODAL\models\best_model.pth
2025-08-01 11:54:19 - pulsar_trainer - INFO - Epoch   2: Train Loss=0.1182, Val Loss=0.0311, Train Acc=94.96%, Val Acc=97.67%
2025-08-01 11:54:19 - pulsar_trainer - INFO - Validation F1: 97.6590, Precision: 97.6533, Recall: 97.6723
2025-08-01 11:54:19 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_1_5_MULTIMODAL\models\best_model.pth
2025-08-01 11:54:32 - pulsar_trainer - INFO - Epoch   3: Train Loss=0.0459, Val Loss=0.0401, Train Acc=97.19%, Val Acc=97.95%
2025-08-01 11:54:32 - pulsar_trainer - INFO - Validation F1: 97.9737, Precision: 98.0241, Recall: 97.9516
2025-08-01 11:54:32 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_1_5_MULTIMODAL\models\best_model.pth
2025-08-01 11:54:45 - pulsar_trainer - INFO - Epoch   4: Train Loss=0.0468, Val Loss=0.0313, Train Acc=97.05%, Val Acc=98.32%
2025-08-01 11:54:45 - pulsar_trainer - INFO - Validation F1: 98.3240, Precision: 98.3240, Recall: 98.3240
2025-08-01 11:54:46 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_1_5_MULTIMODAL\models\best_model.pth
2025-08-01 11:54:59 - pulsar_trainer - INFO - Epoch   5: Train Loss=0.0369, Val Loss=0.0299, Train Acc=97.83%, Val Acc=98.42%
2025-08-01 11:54:59 - pulsar_trainer - INFO - Validation F1: 98.4292, Precision: 98.4555, Recall: 98.4171
2025-08-01 11:54:59 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_1_5_MULTIMODAL\models\best_model.pth
2025-08-01 11:55:12 - pulsar_trainer - INFO - Epoch   6: Train Loss=0.0331, Val Loss=0.0289, Train Acc=97.81%, Val Acc=98.32%
2025-08-01 11:55:12 - pulsar_trainer - INFO - Validation F1: 98.3490, Precision: 98.4291, Recall: 98.3240
2025-08-01 11:55:25 - pulsar_trainer - INFO - Epoch   7: Train Loss=0.0356, Val Loss=0.0220, Train Acc=97.79%, Val Acc=98.70%
2025-08-01 11:55:25 - pulsar_trainer - INFO - Validation F1: 98.6965, Precision: 98.6965, Recall: 98.6965
2025-08-01 11:55:25 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_1_5_MULTIMODAL\models\best_model.pth
2025-08-01 11:55:38 - pulsar_trainer - INFO - Epoch   8: Train Loss=0.0278, Val Loss=0.0231, Train Acc=98.33%, Val Acc=98.51%
2025-08-01 11:55:38 - pulsar_trainer - INFO - Validation F1: 98.5035, Precision: 98.5016, Recall: 98.5102
2025-08-01 11:55:50 - pulsar_trainer - INFO - Epoch   9: Train Loss=0.0242, Val Loss=0.0232, Train Acc=98.45%, Val Acc=98.79%
2025-08-01 11:55:50 - pulsar_trainer - INFO - Validation F1: 98.7988, Precision: 98.8224, Recall: 98.7896
2025-08-01 11:55:51 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_1_5_MULTIMODAL\models\best_model.pth
2025-08-01 11:56:04 - pulsar_trainer - INFO - Epoch  10: Train Loss=0.0258, Val Loss=0.0262, Train Acc=98.37%, Val Acc=98.23%
2025-08-01 11:56:04 - pulsar_trainer - INFO - Validation F1: 98.2083, Precision: 98.2235, Recall: 98.2309
2025-08-01 11:56:16 - pulsar_trainer - INFO - Epoch  11: Train Loss=0.0218, Val Loss=0.0326, Train Acc=98.77%, Val Acc=97.30%
2025-08-01 11:56:16 - pulsar_trainer - INFO - Validation F1: 97.2171, Precision: 97.3379, Recall: 97.2998
2025-08-01 11:56:29 - pulsar_trainer - INFO - Epoch  12: Train Loss=0.0211, Val Loss=0.0180, Train Acc=98.79%, Val Acc=98.70%
2025-08-01 11:56:29 - pulsar_trainer - INFO - Validation F1: 98.7022, Precision: 98.7127, Recall: 98.6965
2025-08-01 11:56:42 - pulsar_trainer - INFO - Epoch  13: Train Loss=0.0247, Val Loss=0.0281, Train Acc=98.37%, Val Acc=98.04%
2025-08-01 11:56:42 - pulsar_trainer - INFO - Validation F1: 98.0149, Precision: 98.0382, Recall: 98.0447
2025-08-01 11:56:55 - pulsar_trainer - INFO - Epoch  14: Train Loss=0.0213, Val Loss=0.0374, Train Acc=98.92%, Val Acc=98.23%
2025-08-01 11:56:55 - pulsar_trainer - INFO - Validation F1: 98.2289, Precision: 98.2272, Recall: 98.2309
2025-08-01 11:57:08 - pulsar_trainer - INFO - Epoch  15: Train Loss=0.0252, Val Loss=0.0186, Train Acc=98.61%, Val Acc=98.98%
2025-08-01 11:57:08 - pulsar_trainer - INFO - Validation F1: 98.9746, Precision: 98.9738, Recall: 98.9758
2025-08-01 11:57:08 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_1_5_MULTIMODAL\models\best_model.pth
2025-08-01 11:57:21 - pulsar_trainer - INFO - Epoch  16: Train Loss=0.0184, Val Loss=0.0185, Train Acc=98.90%, Val Acc=98.60%
2025-08-01 11:57:21 - pulsar_trainer - INFO - Validation F1: 98.6140, Precision: 98.6390, Recall: 98.6034
2025-08-01 11:57:34 - pulsar_trainer - INFO - Epoch  17: Train Loss=0.0180, Val Loss=0.0194, Train Acc=98.88%, Val Acc=98.98%
2025-08-01 11:57:34 - pulsar_trainer - INFO - Validation F1: 98.9836, Precision: 99.0059, Recall: 98.9758
2025-08-01 11:57:47 - pulsar_trainer - INFO - Epoch  18: Train Loss=0.0170, Val Loss=0.0378, Train Acc=99.16%, Val Acc=98.70%
2025-08-01 11:57:47 - pulsar_trainer - INFO - Validation F1: 98.7159, Precision: 98.7910, Recall: 98.6965
2025-08-01 11:58:00 - pulsar_trainer - INFO - Epoch  19: Train Loss=0.0266, Val Loss=0.0217, Train Acc=98.75%, Val Acc=98.70%
2025-08-01 11:58:00 - pulsar_trainer - INFO - Validation F1: 98.6965, Precision: 98.6965, Recall: 98.6965
2025-08-01 11:58:12 - pulsar_trainer - INFO - Epoch  20: Train Loss=0.0204, Val Loss=0.0155, Train Acc=98.88%, Val Acc=99.07%
2025-08-01 11:58:12 - pulsar_trainer - INFO - Validation F1: 99.0689, Precision: 99.0689, Recall: 99.0689
2025-08-01 11:58:13 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_1_5_MULTIMODAL\models\best_model.pth
2025-08-01 11:58:26 - pulsar_trainer - INFO - Epoch  21: Train Loss=0.0217, Val Loss=0.0262, Train Acc=98.61%, Val Acc=98.51%
2025-08-01 11:58:26 - pulsar_trainer - INFO - Validation F1: 98.5000, Precision: 98.5009, Recall: 98.5102
2025-08-01 11:58:38 - pulsar_trainer - INFO - Epoch  22: Train Loss=0.0223, Val Loss=0.0212, Train Acc=98.73%, Val Acc=99.26%
2025-08-01 11:58:38 - pulsar_trainer - INFO - Validation F1: 99.2584, Precision: 99.2665, Recall: 99.2551
2025-08-01 11:58:39 - pulsar_trainer - INFO - 模型已保存: D:\pulsarSuanfa\outputs_1_5_MULTIMODAL\models\best_model.pth
2025-08-01 11:58:52 - pulsar_trainer - INFO - Epoch  23: Train Loss=0.0206, Val Loss=0.0958, Train Acc=98.85%, Val Acc=98.70%
2025-08-01 11:58:52 - pulsar_trainer - INFO - Validation F1: 98.7078, Precision: 98.7378, Recall: 98.6965
2025-08-01 11:59:04 - pulsar_trainer - INFO - Epoch  24: Train Loss=0.0487, Val Loss=0.0277, Train Acc=98.31%, Val Acc=97.77%
2025-08-01 11:59:04 - pulsar_trainer - INFO - Validation F1: 97.7703, Precision: 97.7764, Recall: 97.7654
2025-08-01 11:59:17 - pulsar_trainer - INFO - Epoch  25: Train Loss=0.0205, Val Loss=0.0198, Train Acc=98.96%, Val Acc=98.98%
2025-08-01 11:59:17 - pulsar_trainer - INFO - Validation F1: 98.9858, Precision: 99.0194, Recall: 98.9758
2025-08-01 11:59:30 - pulsar_trainer - INFO - Epoch  26: Train Loss=0.0214, Val Loss=0.0325, Train Acc=99.06%, Val Acc=98.70%
2025-08-01 11:59:30 - pulsar_trainer - INFO - Validation F1: 98.7159, Precision: 98.7910, Recall: 98.6965
2025-08-01 11:59:43 - pulsar_trainer - INFO - Epoch  27: Train Loss=0.0167, Val Loss=0.0576, Train Acc=99.14%, Val Acc=96.93%
2025-08-01 11:59:43 - pulsar_trainer - INFO - Validation F1: 97.0282, Precision: 97.4057, Recall: 96.9274
2025-08-01 11:59:56 - pulsar_trainer - INFO - Epoch  28: Train Loss=0.0164, Val Loss=0.0173, Train Acc=99.12%, Val Acc=99.07%
2025-08-01 11:59:56 - pulsar_trainer - INFO - Validation F1: 99.0750, Precision: 99.0918, Recall: 99.0689
2025-08-01 12:00:09 - pulsar_trainer - INFO - Epoch  29: Train Loss=0.0185, Val Loss=0.0263, Train Acc=98.92%, Val Acc=98.60%
2025-08-01 12:00:09 - pulsar_trainer - INFO - Validation F1: 98.6169, Precision: 98.6540, Recall: 98.6034
2025-08-01 12:00:21 - pulsar_trainer - INFO - Epoch  30: Train Loss=0.0229, Val Loss=0.0259, Train Acc=98.73%, Val Acc=98.51%
2025-08-01 12:00:21 - pulsar_trainer - INFO - Validation F1: 98.5000, Precision: 98.5009, Recall: 98.5102
2025-08-01 12:00:34 - pulsar_trainer - INFO - Epoch  31: Train Loss=10.5559, Val Loss=45.4631, Train Acc=96.10%, Val Acc=97.21%
2025-08-01 12:00:34 - pulsar_trainer - INFO - Validation F1: 97.2004, Precision: 97.1953, Recall: 97.2067
2025-08-01 12:00:47 - pulsar_trainer - INFO - Epoch  32: Train Loss=36.7369, Val Loss=2.2355, Train Acc=93.93%, Val Acc=94.69%
2025-08-01 12:00:47 - pulsar_trainer - INFO - Validation F1: 94.3426, Precision: 94.8085, Recall: 94.6927
2025-08-01 12:01:00 - pulsar_trainer - INFO - Epoch  33: Train Loss=1.1201, Val Loss=0.2947, Train Acc=97.03%, Val Acc=97.77%
2025-08-01 12:01:00 - pulsar_trainer - INFO - Validation F1: 97.7501, Precision: 97.7456, Recall: 97.7654
2025-08-01 12:01:13 - pulsar_trainer - INFO - Epoch  34: Train Loss=0.3826, Val Loss=0.2011, Train Acc=97.21%, Val Acc=95.53%
2025-08-01 12:01:13 - pulsar_trainer - INFO - Validation F1: 95.2985, Precision: 95.5988, Recall: 95.5307
2025-08-01 12:01:26 - pulsar_trainer - INFO - Epoch  35: Train Loss=0.2571, Val Loss=0.2749, Train Acc=96.83%, Val Acc=98.23%
2025-08-01 12:01:26 - pulsar_trainer - INFO - Validation F1: 98.2208, Precision: 98.2184, Recall: 98.2309
2025-08-01 12:01:39 - pulsar_trainer - INFO - Epoch  36: Train Loss=0.2125, Val Loss=0.1476, Train Acc=97.09%, Val Acc=98.14%
2025-08-01 12:01:39 - pulsar_trainer - INFO - Validation F1: 98.1250, Precision: 98.1233, Recall: 98.1378
2025-08-01 12:01:51 - pulsar_trainer - INFO - Epoch  37: Train Loss=0.0919, Val Loss=0.0827, Train Acc=97.27%, Val Acc=93.58%
2025-08-01 12:01:51 - pulsar_trainer - INFO - Validation F1: 93.0215, Precision: 93.7691, Recall: 93.5754
2025-08-01 12:01:51 - pulsar_trainer - INFO - 早停触发，在第 37 轮停止训练
2025-08-01 12:01:51 - pulsar_trainer - INFO - ============================================================
2025-08-01 12:01:51 - pulsar_trainer - INFO - ✅ 训练完成
2025-08-01 12:01:51 - pulsar_trainer - INFO - 🏆 最佳验证准确率: 99.2551
2025-08-01 12:01:51 - pulsar_trainer - INFO - ⏱️ 总训练时间: 527.57秒
2025-08-01 12:01:51 - pulsar_trainer - INFO - ============================================================
2025-08-01 12:01:51 - pulsar_trainer - INFO - 🔍 开始最终评估...
2025-08-01 12:01:52 - pulsar_trainer - INFO - 模型已加载: D:\pulsarSuanfa\outputs_1_5_MULTIMODAL\models\best_model.pth
2025-08-01 12:02:15 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 开始全面误分类分析...
2025-08-01 12:02:15 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 综合误分类分析报告已生成: D:\pulsarSuanfa\outputs_1_5_MULTIMODAL\results\comprehensive_misclassification_analysis\comprehensive_misclassification_report.txt
2025-08-01 12:02:15 - pulsar_trainer.utils.comprehensive_misclassification_analysis - INFO - 全面误分类分析完成。报告保存至: D:\pulsarSuanfa\outputs_1_5_MULTIMODAL\results\comprehensive_misclassification_analysis
2025-08-01 12:02:15 - pulsar_trainer - INFO - 📊 评估结果:
2025-08-01 12:02:15 - pulsar_trainer - INFO -   - accuracy: 0.9889
2025-08-01 12:02:15 - pulsar_trainer - INFO -   - precision: 0.9516
2025-08-01 12:02:15 - pulsar_trainer - INFO -   - recall: 0.9833
2025-08-01 12:02:15 - pulsar_trainer - INFO -   - specificity: 0.9900
2025-08-01 12:02:15 - pulsar_trainer - INFO -   - f1_score: 0.9672
2025-08-01 12:02:15 - pulsar_trainer - INFO -   - false_positive_rate: 0.0100
2025-08-01 12:02:18 - pulsar_trainer - INFO - 所有结果已保存到: D:\pulsarSuanfa\outputs_1_5_MULTIMODAL\results
2025-08-01 12:02:18 - pulsar_trainer - INFO - 可视化图表已保存到: D:\pulsarSuanfa\outputs_1_5_MULTIMODAL\plots
